package prometheus

import (
	"strconv"
	"time"
	"victoriaMetricsCollector/global"
	"victoriaMetricsCollector/service/process"

	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"
)

var (
	NA string = "NA"
)

// TimeSeriesDataList 时间序列数据列表
type TimeSeriesDataList []TimeSeriesData

// TimeSeriesData 时间序列数据
type TimeSeriesData struct {
	Value  float64
	Labels map[string]string
}

// 指标信息
type MetricsInfo struct {
	Name       string   // 指标名称
	Help       string   // 指标说明
	MetricType string   // 指标类型, counter, gauge, histogram, summary
	ValueKey   string   // 指标值的key, 根据获取数据map获取
	LabelNames []string // 指标标签, 根据获取数据map获取
}

// DynamicCollector 动态指标收集器
type DynamicCollector struct {
	isStart               bool                               // 是否开始采集
	zapLog                *zap.Logger                        // ZAP日志
	ScrapeInterval        int64                              // 抓取间隔
	ScrapeBufferSize      int64                              // 缓存数据量
	MetricsInfoList       []MetricsInfo                      // 指标信息列表
	GagueVec              *prometheus.GaugeVec               // 单个采集器
	GagueVecList          map[string]*prometheus.GaugeVec    // 多个采集器, key为指标名称, value为指标描述
	TimeSeriesDataChan    chan TimeSeriesDataList            // 异步业务数据通道
	TimeSeriesMapDataChan chan map[string]TimeSeriesDataList // 异步业务数据通道, 用于多个采集器，key为指标名称
}

// NewCollectorWithOneMetric 创建单指标动态指标收集器
// func NewCollectorWithOneMetric() *DynamicCollector {

// 	d := DynamicCollector{
// 		isStart:        false,
// 		ScrapeInterval: 10,
// 		GagueVec: prometheus.NewGaugeVec(
// 			prometheus.GaugeOpts{
// 				Name: "myapp_mymetric",
// 				Help: "This is my custom metric with dynamic labels",
// 			},
// 			[]string{
// 				"name",
// 				"pid",
// 				"ppid",
// 				"cmdline",
// 				"start_time",
// 				"exe",
// 				"uid",
// 				"username",
// 			}, // 声明所有可能的标签键
// 		),
// 	}

// 	// 异步获取业务数据, 回调函数返回TimeSeriesDataList
// 	d.DataFetcherOneASync(fromatData)

// 	return &d
// }

// 异步获取业务数据, 回调函数返回TimeSeriesDataList
// func (dc *DynamicCollector) DataFetcherOneASync(dataFetcher func() TimeSeriesDataList) {
// 	// 初始化通道
// 	dc.TimeSeriesDataChan = make(chan TimeSeriesDataList)

// 	go func() {

// 		for {
// 			// 保持通道有一个有效数据作为缓存
// 			if len(dc.TimeSeriesDataChan) <= 1 {
// 				// 发送到通道
// 				dc.TimeSeriesDataChan <- dataFetcher()
// 				continue
// 			} else {
// 				// 等待1秒
// 				time.Sleep(time.Duration(dc.ScrapeInterval) * time.Second)
// 				// 发送到通道
// 				dc.TimeSeriesDataChan <- dataFetcher()
// 			}
// 		}
// 	}()

// }

// func fromatData() TimeSeriesDataList {

// 	labels, err := process.SysInfo()
// 	if err != nil {
// 		log.Println(err)
// 	}

// 	var b TimeSeriesDataList

// 	for _, label := range labels {
// 		val, _ := strconv.ParseFloat(label["cpu_percent"], 64)
// 		delete(label, "cpu_percent")
// 		delete(label, "vms")
// 		delete(label, "rss")
// 		b = append(b, TimeSeriesData{
// 			Value:  val,
// 			Labels: label,
// 		})
// 	}

// 	return b
// }

func NewCollectorWithPsutil(scrapeInterval, scrapeBufferSize int64) *DynamicCollector {

	// 实例化
	d := DynamicCollector{
		isStart:          false,
		ScrapeBufferSize: scrapeBufferSize,
		ScrapeInterval:   scrapeInterval,
		zapLog:           global.Setting.Logger,
		GagueVecList:     make(map[string]*prometheus.GaugeVec),
	}

	// 数据提供者
	// 异步获取业务数据, 传入回调函数, 返回 ([]map[string]string, error)
	// d.DataFetcherASync(process.SysInfo)
	d.DataFetcherASync(process.PsUtil)

	d.zapLog.Sugar().Infof("抓取间隔: %d, 缓存数据大小: %d", scrapeInterval, scrapeBufferSize)

	// 定义指标
	m0 := MetricsInfo{
		Name:       "process_info",
		Help:       "Process Basic Info",
		MetricType: "gauge",
		ValueKey:   "1",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
			"start_time",
			"exe",
			"uid",
			"username",
			"state",
		},
	}

	m1 := MetricsInfo{
		Name:       "process_cpu_percent",
		Help:       "Process CPU usage, base on running time, like ps cmd",
		MetricType: "gauge",
		ValueKey:   "cpu_percent",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m11 := MetricsInfo{
		Name:       "process_cpu_user",
		Help:       "Process CPU user time",
		MetricType: "gauge",
		ValueKey:   "cpu_user",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m12 := MetricsInfo{
		Name:       "process_cpu_system",
		Help:       "Process CPU system time",
		MetricType: "gauge",
		ValueKey:   "cpu_system",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m2 := MetricsInfo{
		Name:       "process_rss", // 常驻内存(RSS)
		Help:       "Process Mem RSS usage, base on current",
		MetricType: "gauge",
		ValueKey:   "rss",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m22 := MetricsInfo{
		Name:       "process_vms", // 虚拟内存(VMS)
		Help:       "Process Mem VMS usage, base on current",
		MetricType: "gauge",
		ValueKey:   "vms",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m3 := MetricsInfo{
		Name:       "process_thread_count",
		Help:       "Process Thread Count",
		MetricType: "gauge",
		ValueKey:   "thread_count",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m4 := MetricsInfo{
		Name:       "process_file_descriptors",
		Help:       "Process File Descriptors",
		MetricType: "gauge",
		ValueKey:   "file_descriptors",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m5 := MetricsInfo{
		Name:       "process_io_read_count",
		Help:       "Process IO Read Count",
		MetricType: "gauge",
		ValueKey:   "io_read_count",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m6 := MetricsInfo{
		Name:       "process_io_write_count",
		Help:       "Process IO Write Count",
		MetricType: "gauge",
		ValueKey:   "io_write_count",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m7 := MetricsInfo{
		Name:       "process_io_read_bytes",
		Help:       "Process IO Read Bytes",
		MetricType: "gauge",
		ValueKey:   "io_read_bytes",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m8 := MetricsInfo{
		Name:       "process_io_write_bytes",
		Help:       "Process IO Write Bytes",
		MetricType: "gauge",
		ValueKey:   "io_write_bytes",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m33 := MetricsInfo{
		Name:       "process_net_connection",
		Help:       "Process Network Connection Num",
		MetricType: "gauge",
		ValueKey:   "net_connections",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
			"tcp_ports",
			"udp_ports",
		},
	}

	metricsInfoList := []MetricsInfo{
		m0, m1, m11, m12, m2, m22, m3, m4, m5, m6, m7, m8, m33,
	}

	// 添加指标
	for _, metricsInfo := range metricsInfoList {
		err := d.AddGagueMetric(metricsInfo)
		if err != nil {
			d.zapLog.Sugar().Errorf("AddGagueMetric 添加指标失败: %v", err)
		}
	}

	return &d
}

func NewCollectorWithSysInfo(scrapeInterval, scrapeBufferSize int64) *DynamicCollector {

	// 实例化
	d := DynamicCollector{
		isStart:          false,
		ScrapeBufferSize: scrapeBufferSize,
		ScrapeInterval:   scrapeInterval,
		zapLog:           global.Setting.Logger,
		GagueVecList:     make(map[string]*prometheus.GaugeVec),
	}

	// 数据提供者
	// 异步获取业务数据, 传入回调函数, 返回 ([]map[string]string, error)
	// d.DataFetcherASync(process.SysInfo)
	d.DataFetcherASync(process.SysInfo)

	d.zapLog.Sugar().Infof("抓取间隔: %d, 缓存数据大小: %d", scrapeInterval, scrapeBufferSize)

	// 定义指标
	m0 := MetricsInfo{
		Name:       "process_info",
		Help:       "Process Basic Info by sysinfo",
		MetricType: "gauge",
		ValueKey:   "1",
		LabelNames: []string{
			"name",
			"pid",
			"ppid",
			"cmdline",
			"start_time",
			"exe",
			"uid",
			"username",
		},
	}

	m1 := MetricsInfo{
		Name:       "process_cpu_total",
		Help:       "Process CPU user total by sysinfo",
		MetricType: "gauge",
		ValueKey:   "cpu_total",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m11 := MetricsInfo{
		Name:       "process_cpu_user",
		Help:       "Process CPU user time by sysinfo",
		MetricType: "gauge",
		ValueKey:   "cpu_user",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m12 := MetricsInfo{
		Name:       "process_cpu_system",
		Help:       "Process CPU system time by sysinfo",
		MetricType: "gauge",
		ValueKey:   "cpu_system",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m13 := MetricsInfo{
		Name:       "process_cpu_percent",
		Help:       "Process CPU percent based on running time by sysinfo",
		MetricType: "gauge",
		ValueKey:   "cpu_percent",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	m2 := MetricsInfo{
		Name:       "process_rss", // 常驻内存(RSS)
		Help:       "Process Mem RSS usage by sysinfo",
		MetricType: "gauge",
		ValueKey:   "rss",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}
	m22 := MetricsInfo{
		Name:       "process_vms", // 虚拟内存(VMS)
		Help:       "Process Mem VMS usage by sysinfo",
		MetricType: "gauge",
		ValueKey:   "vms",
		LabelNames: []string{
			"pid",
			"ppid",
			"name",
			"cmdline",
		},
	}

	metricsInfoList := []MetricsInfo{
		m0, m1, m11, m12, m13, m2, m22,
	}

	// 添加指标
	for _, metricsInfo := range metricsInfoList {
		err := d.AddGagueMetric(metricsInfo)
		if err != nil {
			d.zapLog.Sugar().Errorf("AddGagueMetric 添加指标失败: %v", err)
		}
	}

	return &d
}

func (dc *DynamicCollector) AddGagueMetric(metricsInfo MetricsInfo) error {

	dc.zapLog.Sugar().Infof("AddGagueMetric 添加指标: %s, 指标标签: %v", metricsInfo.Name, metricsInfo.LabelNames)

	switch metricsInfo.MetricType {
	case "gauge":
		gaugeVec := prometheus.NewGaugeVec(
			// 指标描述
			prometheus.GaugeOpts{
				Name: metricsInfo.Name,
				Help: metricsInfo.Help,
			},
			// 声明所有可能的标签键
			metricsInfo.LabelNames,
		)

		if _, ok := dc.GagueVecList[metricsInfo.Name]; !ok {
			// 添加指标
			dc.GagueVecList[metricsInfo.Name] = gaugeVec
		} else {
			return errors.New("指标已存在")
		}
	default:
		return errors.New("不支持的指标类型")
	}

	dc.zapLog.Sugar().Infof("AddGagueMetric 成功添加指标: %s", metricsInfo.Name)

	// 添加指标信息, 用于Collect前的数据校对
	dc.MetricsInfoList = append(dc.MetricsInfoList, metricsInfo)

	return nil

}

func (dc *DynamicCollector) DataFetcherASync(dataFetcher func() ([]map[string]string, error)) {
	// 初始化通道
	dc.TimeSeriesMapDataChan = make(chan map[string]TimeSeriesDataList, dc.ScrapeBufferSize)

	go func() {

		for {
			start := time.Now()
			res, err := dataFetcher()

			diff := time.Since(start).Milliseconds()
			dc.zapLog.Sugar().Infof("DataFetcherASync 抓取数据耗时: %d ms, 数据量: %d", diff, len(res))

			if err != nil {
				dc.zapLog.Sugar().Errorf("DataFetcherASync 抓取数据失败: %v", err)
				continue
			}

			dc.zapLog.Sugar().Debugf("DataFetcherASync 发送数据到通道")
			dc.TimeSeriesMapDataChan <- dc.PreFromatData(res)

			time.Sleep(time.Millisecond * time.Duration(dc.ScrapeInterval))
		}
	}()

}

// 预处理数据, 将一份数据拆分成多个指标
func (dc *DynamicCollector) PreFromatData(dataSet []map[string]string) map[string]TimeSeriesDataList {
	dc.zapLog.Debug("PreFromatData 开始将数据拆分到对应指标")

	b := make(map[string]TimeSeriesDataList)

	// cpu or rss 指标类型
	for _, metricsInfo := range dc.MetricsInfoList {
		dc.zapLog.Sugar().Debugf("PreFromatData 开始处理指标: %s", metricsInfo.Name)

		// 指标数据列表
		var timeSeriesDataList TimeSeriesDataList

		// 遍历每一条数据
		for _, data := range dataSet {
			var val float64

			dc.zapLog.Sugar().Debugf("PreFromatData 配置指标值: %s", metricsInfo.Name)

			// 常量值
			if metricsInfo.ValueKey == "1" {
				val = 1
			} else {
				// 检查指标值是否存在
				if v, ok := data[metricsInfo.ValueKey]; ok {

					dc.zapLog.Sugar().Debugf("PreFromatData 指标值存在: %s, 值: %v", metricsInfo.Name, v)

					// 检查指标值是否能转换为float64
					value, err := strconv.ParseFloat(v, 64)
					if err != nil {
						dc.zapLog.Sugar().Errorf("PreFromatData 指标值转换失败: %s, 值: %v, 错误: %v", metricsInfo.Name, v, err)
						continue
					} else {
						val = value
					}

				} else {
					dc.zapLog.Sugar().Debugf("PreFromatData 指标值不存在: %s, 跳过", metricsInfo.Name)
					continue
				}

			}

			// 获取标签值
			dc.zapLog.Debug("PreFromatData 获取标签值")
			timeSeriesLabel := make(map[string]string)

			// 检查标签值是否存在
			for _, labelName := range metricsInfo.LabelNames {

				// timeSeriesLabel[labelName] = data[labelName]
				if v, ok := data[labelName]; ok {
					timeSeriesLabel[labelName] = v
				} else {
					timeSeriesLabel[labelName] = NA
				}
			}

			// 添加到指标数据列表
			dc.zapLog.Sugar().Debugf("PreFromatData 添加到指标数据列表: %s, 值: %v, 标签: %v", metricsInfo.Name, val, timeSeriesLabel)
			timeSeriesDataList = append(timeSeriesDataList, TimeSeriesData{
				Value:  val,
				Labels: timeSeriesLabel,
			})
		}
		// 指标数据key必须和DESC申明的key一致
		b[metricsInfo.Name] = timeSeriesDataList

	}

	return b
}

// 实现Describe方法
func (dc *DynamicCollector) Describe(desc chan<- *prometheus.Desc) {
	if len(dc.GagueVecList) > 0 {
		// 多个指标
		dc.zapLog.Info("Describe 开始配置多个多个指标")
		for name, gagueVec := range dc.GagueVecList {
			// 向Prometheus注册表描述这个GaugeVec将要提供的指标结构。
			// Describe方法会创建一个*prometheus.Desc对象并发送到desc通道，告诉Prometheus：
			// 指标名称 - 如process_cpu
			// 指标帮助信息 - 如"Process CPU usage"
			// 可变标签键 - 如["pid", "ppid", "name", ...]
			// 固定标签 - 通常为空
			dc.zapLog.Sugar().Infof("Describe 配置指标: %s", name)
			gagueVec.Describe(desc)
		}
	} else {
		// 单个指标
		dc.GagueVec.Describe(desc)
	}
}

// 实现Collect方法
func (dc *DynamicCollector) Collect(metric chan<- prometheus.Metric) {
	if len(dc.GagueVecList) > 0 {
		// 多个指标
		select {
		case timeSeriesDataMap := <-dc.TimeSeriesMapDataChan:
			dc.zapLog.Debug("Collect 从通道接收数据")
			for k, v := range timeSeriesDataMap {

				if _, ok := dc.GagueVecList[k]; ok {
					dc.zapLog.Sugar().Debugf("Collect 配置指标: %s", k)

					// 重置所有指标（避免旧标签残留）
					dc.GagueVecList[k].Reset()

					// 设置新值
					for _, data := range v {
						dc.zapLog.Sugar().Debugf("Collect 指标: %v, 标签:%v, 值:%s", k, data.Labels, data.Value)
						dc.GagueVecList[k].With(data.Labels).Set(data.Value)
					}

					// 收集指标
					dc.GagueVecList[k].Collect(metric)
					dc.zapLog.Sugar().Debugf("Collect 完成采集指标: %s", k)

				} else {
					// 一般在初始化会导致这样错误, 所以直接Fatalf
					// log.Fatalf("指标申明必须和指标数据一致, 缺失指标数据: %s", k)
					dc.zapLog.Sugar().Fatalf("指标申明必须和指标数据一致, 缺失指标数据: %s", k)
				}
			}
		default:
			time.Sleep(time.Millisecond * 500)
			dc.zapLog.Sugar().Warnf("Collect 采集频率过快, 没有新数据, 等待几秒再次拉取")

		}

	} else {
		// 单个指标
		select {
		case timeSeriesData := <-dc.TimeSeriesDataChan:
			// 重置所有指标（避免旧标签残留）
			dc.GagueVec.Reset()

			// 设置新值
			for _, data := range timeSeriesData {
				dc.GagueVec.With(data.Labels).Set(data.Value)
			}
			// 收集指标
			dc.GagueVec.Collect(metric)

			// 标记开始采集
			dc.isStart = true
		default:
			// 没有新数据 但已经开始采集过一次后
			if dc.isStart {
				// 采集旧指标
				dc.GagueVec.Collect(metric)
			}
		}

	}
}
