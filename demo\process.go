package demo

import (
	"fmt"
	"os"
	"os/user"
	"strconv"
	"strings"
	"time"

	"github.com/prometheus/procfs"
)

// LinuxProcessInfo 存储Linux进程的详细信息
type LinuxProcessInfo struct {
	Name            string   // 进程名称
	PID             int      // 进程ID
	PPID            int      // 父进程ID
	Cmdline         string   // 命令行参数
	StartTime       string   // 启动时间
	ExePath         string   // 可执行文件路径
	UID             string   // 用户ID
	Username        string   // 用户名
	RSS             uint64   // 常驻内存(RSS)
	VMS             uint64   // 虚拟内存(VMS)
	CPUUser         float64  // 用户态CPU时间
	CPUSystem       float64  // 系统态CPU时间
	IOReadCount     uint64   // IO读取次数
	IOWriteCount    uint64   // IO写入次数
	IOReadBytes     uint64   // IO读取字节数
	IOWriteBytes    uint64   // IO写入字节数
	NetConnections  int      // 网络连接数
	TCPPorts        []uint16 // TCP监听端口
	UDPPorts        []uint16 // UDP监听端口
	State           string   // 进程状态
	ThreadCount     int      // 线程数
	FileDescriptors int      // 文件描述符数量
}

// GetLinuxProcessInfo 获取Linux系统上的进程信息
func GetLinuxProcessInfo() ([]LinuxProcessInfo, error) {
	// 使用procfs库获取进程列表
	fs, err := procfs.NewFS("/proc")
	if err != nil {
		return nil, fmt.Errorf("无法访问/proc文件系统: %v", err)
	}

	procs, err := fs.AllProcs()
	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	var result []LinuxProcessInfo
	for _, proc := range procs {
		info, err := getDetailedProcessInfo(proc, fs)
		if err != nil {
			// 记录错误但继续处理其他进程
			fmt.Printf("获取进程 %d 信息失败: %v\n", proc.PID, err)
			continue
		}
		result = append(result, info)
	}

	return result, nil
}

// getDetailedProcessInfo 获取单个进程的详细信息
func getDetailedProcessInfo(proc procfs.Proc, fs procfs.FS) (LinuxProcessInfo, error) {
	var info LinuxProcessInfo
	var err error

	// 基本信息: PID
	info.PID = proc.PID

	// 进程状态信息
	status, err := proc.Stat()
	if err != nil {
		return info, fmt.Errorf("读取进程状态失败: %v", err)
	}

	// 名称和PPID
	info.Name = status.Comm
	info.PPID = status.PPID
	info.State = string(status.State)
	info.ThreadCount = status.NumThreads

	// 命令行参数
	cmdline, err := proc.CmdLine()
	if err == nil {
		info.Cmdline = strings.Join(cmdline, " ")
	}

	// 可执行文件路径
	exe, err := os.Readlink(fmt.Sprintf("/proc/%d/exe", proc.PID))
	if err == nil {
		info.ExePath = exe
	}

	// 启动时间
	bootTime, err := fs.Stat()
	if err == nil {
		startTime := time.Unix(int64(bootTime.BootTime), 0).Add(
			time.Second * time.Duration(status.Starttime) / 100)
		info.StartTime = startTime.Format("2006-01-02 15:04:05")
	}

	// 用户信息
	statusInfo, err := proc.NewStatus()
	if err == nil {
		uidStr := strconv.FormatUint(uint64(statusInfo.UIDs[0]), 10)
		info.UID = uidStr

		// 获取用户名
		if u, err := user.LookupId(uidStr); err == nil {
			info.Username = u.Username
		}
	}

	// 内存信息
	info.RSS = uint64(status.ResidentMemory())
	info.VMS = uint64(status.VirtualMemory())

	// CPU时间
	info.CPUUser = float64(status.UTime) / 100.0
	info.CPUSystem = float64(status.STime) / 100.0

	// IO统计
	io, err := proc.IO()
	if err == nil {
		info.IOReadCount = io.RChar
		info.IOWriteCount = io.WChar
		info.IOReadBytes = io.ReadBytes
		info.IOWriteBytes = io.WriteBytes
	}

	// 网络连接和端口 - 从/proc/net读取
	tcpPorts, udpPorts, connCount := getProcessPorts(proc.PID)
	info.TCPPorts = tcpPorts
	info.UDPPorts = udpPorts
	info.NetConnections = connCount

	// 文件描述符数量
	fds, err := os.ReadDir(fmt.Sprintf("/proc/%d/fd", proc.PID))
	if err == nil {
		info.FileDescriptors = len(fds)
	}

	return info, nil
}

// FormatProcessInfo 将进程信息格式化为map[string]string
func FormatProcessInfo(info LinuxProcessInfo) map[string]string {
	result := make(map[string]string)

	result["name"] = info.Name
	result["pid"] = strconv.Itoa(info.PID)
	result["ppid"] = strconv.Itoa(info.PPID)
	result["cmdline"] = info.Cmdline
	result["start_time"] = info.StartTime
	result["exe"] = info.ExePath
	result["uid"] = info.UID
	result["username"] = info.Username
	result["rss"] = strconv.FormatUint(info.RSS, 10)
	result["vms"] = strconv.FormatUint(info.VMS, 10)
	result["cpu_user"] = strconv.FormatFloat(info.CPUUser, 'f', 2, 64)
	result["cpu_system"] = strconv.FormatFloat(info.CPUSystem, 'f', 2, 64)
	result["io_read_count"] = strconv.FormatUint(info.IOReadCount, 10)
	result["io_write_count"] = strconv.FormatUint(info.IOWriteCount, 10)
	result["io_read_bytes"] = strconv.FormatUint(info.IOReadBytes, 10)
	result["io_write_bytes"] = strconv.FormatUint(info.IOWriteBytes, 10)
	result["net_connections"] = strconv.Itoa(info.NetConnections)

	// 端口列表
	tcpPorts := make([]string, len(info.TCPPorts))
	for i, port := range info.TCPPorts {
		tcpPorts[i] = strconv.Itoa(int(port))
	}
	result["tcp_ports"] = strings.Join(tcpPorts, ",")

	udpPorts := make([]string, len(info.UDPPorts))
	for i, port := range info.UDPPorts {
		udpPorts[i] = strconv.Itoa(int(port))
	}
	result["udp_ports"] = strings.Join(udpPorts, ",")

	result["state"] = info.State
	result["thread_count"] = strconv.Itoa(info.ThreadCount)
	result["file_descriptors"] = strconv.Itoa(info.FileDescriptors)

	return result
}

// GetProcessInfoLinux 获取所有进程信息并格式化为map[string]string的切片
func GetProcessInfoLinux() []map[string]string {
	processes, err := GetLinuxProcessInfo()
	if err != nil {
		fmt.Printf("获取进程信息失败: %v\n", err)
		return []map[string]string{}
	}

	var result []map[string]string
	for _, proc := range processes {
		result = append(result, FormatProcessInfo(proc))
	}

	// 打印详细结果
	fmt.Printf("获取到 %d 个进程信息:\n", len(result))
	for i, proc := range result {
		fmt.Printf("进程 %d:\n", i+1)
		fmt.Printf("  PID=%s, PPID=%s, 名称=%s\n", proc["pid"], proc["ppid"], proc["name"])
		fmt.Printf("  用户=%s(UID:%s), 状态=%s\n", proc["username"], proc["uid"], proc["state"])
		fmt.Printf("  内存: RSS=%s bytes, VMS=%s bytes\n", proc["rss"], proc["vms"])
		fmt.Printf("  CPU: 用户态=%v, 系统态=%v\n", proc["cpu_user"], proc["cpu_system"])
		fmt.Printf("  线程数=%s, 文件描述符=%s\n", proc["thread_count"], proc["file_descriptors"])
		fmt.Printf("  网络连接数=%s\n", proc["net_connections"])
		if proc["tcp_ports"] != "" {
			fmt.Printf("  TCP端口: %s\n", proc["tcp_ports"])
		}
		if proc["udp_ports"] != "" {
			fmt.Printf("  UDP端口: %s\n", proc["udp_ports"])
		}
		fmt.Printf("  启动时间=%s\n", proc["start_time"])
		fmt.Printf("  执行路径=%s\n", proc["exe"])
		fmt.Println("  ----------------------------------------")
	}

	return result
}

// getProcessPorts 获取进程的监听端口
func getProcessPorts(pid int) ([]uint16, []uint16, int) {
	var tcpPorts, udpPorts []uint16
	connCount := 0

	// 读取TCP连接
	if tcpConns, err := os.ReadFile("/proc/net/tcp"); err == nil {
		ports, count := parseNetConnections(string(tcpConns), pid)
		connCount += count
		// 合并到结果中
		tcpPorts = append(tcpPorts, ports...)
	}

	// 读取UDP连接
	if udpConns, err := os.ReadFile("/proc/net/udp"); err == nil {
		ports, count := parseNetConnections(string(udpConns), pid)
		connCount += count
		// 合并到结果中
		udpPorts = append(udpPorts, ports...)
	}

	return tcpPorts, udpPorts, connCount
}

// parseNetConnections 解析网络连接文件内容
func parseNetConnections(content string, targetPID int) ([]uint16, int) {
	var ports []uint16
	connCount := 0

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if i == 0 || strings.TrimSpace(line) == "" {
			continue // 跳过标题行和空行
		}

		fields := strings.Fields(line)
		if len(fields) < 10 {
			continue
		}

		// 获取inode
		inode := fields[9]
		if inode == "0" {
			continue
		}

		// 检查该inode是否属于目标进程
		if isInodeOwnedByProcess(inode, targetPID) {
			connCount++

			// 解析本地地址和端口
			localAddr := fields[1]
			if strings.Contains(localAddr, ":") {
				parts := strings.Split(localAddr, ":")
				if len(parts) == 2 {
					if portHex, err := strconv.ParseUint(parts[1], 16, 16); err == nil {
						port := uint16(portHex)
						// 检查是否为监听状态 (state == 0A for TCP LISTEN)
						state := fields[3]
						if state == "0A" || state == "07" { // LISTEN or UDP
							ports = append(ports, port)
						}
					}
				}
			}
		}
	}

	return ports, connCount
}

// isInodeOwnedByProcess 检查inode是否属于指定进程
func isInodeOwnedByProcess(inode string, pid int) bool {
	fdDir := fmt.Sprintf("/proc/%d/fd", pid)
	fds, err := os.ReadDir(fdDir)
	if err != nil {
		return false
	}

	for _, fd := range fds {
		linkPath := fmt.Sprintf("%s/%s", fdDir, fd.Name())
		if link, err := os.Readlink(linkPath); err == nil {
			if strings.Contains(link, fmt.Sprintf("socket:[%s]", inode)) {
				return true
			}
		}
	}

	return false
}
