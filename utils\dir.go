package utils

import (
	"fmt"
	"os"
)

// EnsureDirExists 检查目录是否存在，不存在则创建
func EnsureDirExists(dirPath string) error {
	// 检查目录是否存在
	fileInfo, err := os.Stat(dirPath)
	if err == nil {
		// 路径存在，检查是否是目录
		if fileInfo.IsDir() {
			return nil
		}
		return fmt.Errorf("%s 已存在但不是目录", dirPath)
	}

	// 如果错误是因为路径不存在，则创建目录
	if os.IsNotExist(err) {
		// 创建目录（包括所有必要的父目录）
		err = os.MkdirAll(dirPath, 0755)
		if err != nil {
			return fmt.Errorf("无法创建目录 %s: %v", dirPath, err)
		}
		return nil
	}

	// 其他类型的错误
	return fmt.Errorf("检查目录 %s 时出错: %v", dirPath, err)
}
