package auth

import (
	"log"
	"net/http"

	"github.com/gorilla/handlers"
	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// CORS 中间件
func enableCORS(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置 CORS 头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.<PERSON>er().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		w.<PERSON>er().Set("Access-Control-Allow-Credentials", "true")
		w.<PERSON><PERSON>().Set("Access-Control-Max-Age", "86400") // 24小时

		// 如果是预检请求，直接返回
		if r.Method == "OPTIONS" {
			w.Write<PERSON>ead<PERSON>(http.StatusOK)
			return
		}

		// 继续处理请求
		next.ServeHTTP(w, r)
	})
}

func Basic() {
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if _, err := w.Write([]byte("Hello, CORS!")); err != nil {
			log.Printf("Error writing response: %v", err)
		}
	})

	// 应用 CORS 中间件
	handler := enableCORS(mux)

	if err := http.ListenAndServe(":8080", handler); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

func Gorilla() {
	r := mux.NewRouter()
	r.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if _, err := w.Write([]byte("Hello, CORS with Gorilla!")); err != nil {
			log.Printf("Error writing response: %v", err)
		}
	})

	// 配置 CORS
	cors := handlers.CORS(
		handlers.AllowedOrigins([]string{"*"}),
		handlers.AllowedMethods([]string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
		handlers.AllowedHeaders([]string{"Content-Type", "Authorization"}),
		handlers.AllowCredentials(),
	)

	if err := http.ListenAndServe(":8080", cors(r)); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

func Rs() {
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if _, err := w.Write([]byte("Hello, CORS with rs/cors!")); err != nil {
			log.Printf("Error writing response: %v", err)
		}
	})

	// 配置 CORS
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Content-Type", "Authorization"},
		AllowCredentials: true,
		Debug:            true, // 调试模式
	})

	handler := c.Handler(mux)
	if err := http.ListenAndServe(":8080", handler); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}
