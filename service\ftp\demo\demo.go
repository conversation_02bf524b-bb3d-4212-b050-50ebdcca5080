package demo

import (
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jlaffaye/ftp"
	"github.com/schollz/progressbar/v3"
)

func FtpClient() {
	// 定义命令行参数
	putCmd := flag.Bool("put", false, "Upload file/directory to FTP server")
	getCmd := flag.Bool("get", false, "Download file/directory from FTP server")
	srcPath := flag.String("src", "", "Source path (local or remote)")
	destPath := flag.String("dest", "", "Destination path (remote or local)")
	host := flag.String("host", "localhost", "FTP server host")
	port := flag.String("port", "21", "FTP server port")
	username := flag.String("user", "anonymous", "FTP username")
	password := flag.String("pass", "anonymous", "FTP password")
	recursive := flag.Bool("r", false, "Recursive transfer for directories")
	showProgress := flag.Bool("progress", true, "Show progress bar")

	flag.Parse()

	// 验证参数
	if *putCmd == *getCmd {
		log.Fatal("必须指定 --put 或 --get 操作")
	}
	if *srcPath == "" {
		log.Fatal("必须指定 --src 参数")
	}
	if *destPath == "" {
		log.Fatal("必须指定 --dest 参数")
	}

	// 配置 FTP 连接
	ftpConfig := FTPConfig{
		Host:     *host,
		Port:     *port,
		Username: *username,
		Password: *password,
	}

	// 连接 FTP 服务器
	client, err := ConnectFTP(ftpConfig)
	if err != nil {
		log.Fatalf("FTP连接失败: %v", err)
	}
	if err := client.Quit(); err != nil {
		log.Printf("Warning: Error closing FTP connection: %v", err)
	}

	fmt.Printf("成功连接到FTP服务器 %s:%s\n", *host, *port)

	// 执行操作
	if *putCmd {
		if *recursive && isDir(*srcPath) {
			err = UploadDirectory(client, *srcPath, *destPath, *showProgress)
		} else {
			err = SmartUpload(client, *srcPath, *destPath, *showProgress)
		}
		if err != nil {
			log.Fatalf("上传失败: %v", err)
		}
	} else if *getCmd {
		if *recursive {
			err = DownloadDirectory(client, *srcPath, *destPath, *showProgress)
		} else {
			err = SmartDownload(client, *srcPath, *destPath, *showProgress)
		}
		if err != nil {
			log.Fatalf("下载失败: %v", err)
		}
	}
}

type FTPConfig struct {
	Host     string
	Port     string
	Username string
	Password string
}

func ConnectFTP(config FTPConfig) (*ftp.ServerConn, error) {
	addr := fmt.Sprintf("%s:%s", config.Host, config.Port)
	c, err := ftp.Dial(addr, ftp.DialWithTimeout(10*time.Second))
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	err = c.Login(config.Username, config.Password)
	if err != nil {
		return nil, fmt.Errorf("登录失败: %v", err)
	}

	return c, nil
}

func IsBinaryFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	textExtensions := map[string]bool{
		".txt": true, ".csv": true, ".html": true, ".htm": true, ".xml": true,
		".json": true, ".log": true, ".md": true, ".go": true, ".c": true,
		".cpp": true, ".h": true, ".hpp": true, ".java": true, ".py": true,
		".sh": true, ".bat": true, ".php": true, ".js": true, ".css": true,
		".conf": true, ".ini": true, ".yml": true, ".yaml": true,
	}

	_, isText := textExtensions[ext]
	return !isText
}

func isDir(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

func SmartUpload(c *ftp.ServerConn, localPath, remotePath string, showProgress bool) error {
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("无法打开本地文件: %v", err)
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}

	isBinary := IsBinaryFile(localPath)
	if isBinary {
		err = c.Type(ftp.TransferTypeBinary)
	} else {
		err = c.Type(ftp.TransferTypeASCII)
	}
	if err != nil {
		return fmt.Errorf("设置传输模式失败: %v", err)
	}

	remoteDir := filepath.Dir(remotePath)
	if remoteDir != "" && remoteDir != "." {
		err = ensureRemoteDir(c, remoteDir)
		if err != nil {
			return err
		}
	}

	var reader io.Reader = file
	var bar *progressbar.ProgressBar

	if showProgress {
		bar = progressbar.NewOptions64(
			fileInfo.Size(),
			progressbar.OptionSetDescription(fmt.Sprintf("上传 %s", filepath.Base(localPath))),
			progressbar.OptionShowBytes(true),
			progressbar.OptionSetWidth(30),
			progressbar.OptionShowCount(),
			progressbar.OptionOnCompletion(func() {
				fmt.Println()
			}),
		)
		reader = io.TeeReader(file, bar)
	}

	err = c.Stor(filepath.Base(remotePath), reader)
	if err != nil {
		return fmt.Errorf("文件上传失败: %v", err)
	}

	if showProgress && bar != nil {
		if err := bar.Finish(); err != nil {
			log.Printf("Warning: Error finishing progress bar: %v", err)
		}
	}

	fmt.Printf("成功上传: %s -> %s [模式: %s]\n",
		localPath, remotePath, map[bool]string{true: "二进制", false: "文本"}[isBinary])
	return nil
}

func UploadDirectory(c *ftp.ServerConn, localDir, remoteDir string, showProgress bool) error {
	return filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		relPath, err := filepath.Rel(localDir, path)
		if err != nil {
			return err
		}

		remotePath := filepath.Join(remoteDir, relPath)
		return SmartUpload(c, path, remotePath, showProgress)
	})
}

func SmartDownload(c *ftp.ServerConn, remotePath, localPath string, showProgress bool) error {
	isBinary := IsBinaryFile(remotePath)
	if isBinary {
		err := c.Type(ftp.TransferTypeBinary)
		if err != nil {
			return fmt.Errorf("设置二进制模式失败: %v", err)
		}
	} else {
		err := c.Type(ftp.TransferTypeASCII)
		if err != nil {
			return fmt.Errorf("设置ASCII模式失败: %v", err)
		}
	}

	size, err := c.FileSize(remotePath)
	if err != nil {
		return fmt.Errorf("获取文件大小失败: %v", err)
	}

	r, err := c.Retr(remotePath)
	if err != nil {
		return fmt.Errorf("获取远程文件失败: %v", err)
	}
	defer r.Close()

	localDir := filepath.Dir(localPath)
	if localDir != "" {
		err = os.MkdirAll(localDir, 0755)
		if err != nil {
			return fmt.Errorf("创建本地目录失败: %v", err)
		}
	}

	file, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %v", err)
	}
	defer file.Close()

	var writer io.Writer = file
	var bar *progressbar.ProgressBar

	if showProgress {
		bar = progressbar.NewOptions64(
			size,
			progressbar.OptionSetDescription(fmt.Sprintf("下载 %s", filepath.Base(remotePath))),
			progressbar.OptionShowBytes(true),
			progressbar.OptionSetWidth(30),
			progressbar.OptionShowCount(),
			progressbar.OptionOnCompletion(func() {
				fmt.Println()
			}),
		)
		writer = io.MultiWriter(file, bar)
	}

	_, err = io.Copy(writer, r)
	if err != nil {
		return fmt.Errorf("写入本地文件失败: %v", err)
	}

	if showProgress && bar != nil {
		if err := bar.Finish(); err != nil {
			log.Printf("Warning: Error finishing progress bar: %v", err)
		}
	}

	fmt.Printf("成功下载: %s -> %s [模式: %s]\n",
		remotePath, localPath, map[bool]string{true: "二进制", false: "文本"}[isBinary])
	return nil
}

func DownloadDirectory(c *ftp.ServerConn, remoteDir, localDir string, showProgress bool) error {
	walker := c.Walk(remoteDir)
	for walker.Next() {
		if walker.Stat().Type == ftp.EntryTypeFolder {
			continue
		}

		relPath, err := filepath.Rel(remoteDir, walker.Path())
		if err != nil {
			return err
		}

		localPath := filepath.Join(localDir, relPath)
		err = SmartDownload(c, walker.Path(), localPath, showProgress)
		if err != nil {
			return err
		}
	}
	return walker.Err()
}

func ensureRemoteDir(c *ftp.ServerConn, path string) error {
	dirs := strings.Split(path, "/")
	currentPath := ""

	for _, dir := range dirs {
		if dir == "" {
			continue
		}
		currentPath += "/" + dir

		err := c.ChangeDir(currentPath)
		if err != nil {
			err = c.MakeDir(currentPath)
			if err != nil {
				return fmt.Errorf("创建目录 %s 失败: %v", currentPath, err)
			}
			err = c.ChangeDir(currentPath)
			if err != nil {
				return fmt.Errorf("切换目录 %s 失败: %v", currentPath, err)
			}
		}
	}
	return nil
}
