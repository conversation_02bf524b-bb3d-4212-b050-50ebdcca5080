package sqlmonitor

import (
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
)

func GetOrmMySQL() *gorm.DB {
	dsn := "root:1234@tcp(127.0.0.1:3306)/sqlmonitor?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Auto migrate the schema
	//err = db.AutoMigrate(&TaskExec{})
	//if err != nil {
	//	log.Fatalf("Failed to auto migrate: %v", err)
	//}

	return db
}
