package webdriver

import (
	"log"

	"github.com/playwright-community/playwright-go"
)

// 使用 embed 打包 Playwright 浏览器驱动
func Test() {
	pw, err := playwright.Run()
	if err != nil {
		log.Fatalf("could not start playwright: %v", err)
	}
	defer func() {
		if err := pw.Stop(); err != nil {
			log.Printf("could not stop playwright: %v", err)
		}
	}()

	browser, err := pw.Chromium.Launch()
	if err != nil {
		log.Fatalf("could not launch browser: %v", err)
	}
	defer browser.Close()

	// 其他操作...

	page, err := browser.NewPage()
	if err != nil {
		log.Fatalf("could not create page: %v", err)
	}

	_, err = page.Goto("https://example.com")
	if err != nil {
		log.Fatalf("could not goto: %v", err)
	}

	// 点击元素
	err = page.Locator("button#submit").Click()
	if err != nil {
		log.Fatalf("could not click: %v", err)
	}

	// 填写表单
	err = page.Locator("input#username").Fill("myusername")
	if err != nil {
		log.Fatalf("could not fill input: %v", err)
	}

	// 获取文本内容
	text, err := page.Locator("h1").TextContent()
	if err != nil {
		log.Fatalf("could not get text: %v", err)
	}
	log.Println("Heading text:", text)

	// 等待元素出现
	err = page.Locator(".dynamic-element").WaitFor(playwright.LocatorWaitForOptions{
		State: playwright.WaitForSelectorStateAttached,
	})
	if err != nil {
		log.Fatalf("element did not appear: %v", err)
	}

	// 页面截图
	_, err = page.Screenshot(playwright.PageScreenshotOptions{
		Path: playwright.String("screenshot.png"),
	})
	if err != nil {
		log.Fatalf("could not take screenshot: %v", err)
	}

	//  处理弹窗
	page.On("dialog", func(dialog playwright.Dialog) {
		log.Println("Dialog message:", dialog.Message())
		err := dialog.Dismiss()
		if err != nil {
			log.Printf("could not dismiss dialog: %v", err)
		}
	})
}
