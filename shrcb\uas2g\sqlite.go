package uas2g

import (
	"errors"
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
	_ "modernc.org/sqlite"
)

// User 用户模型（一对多：一个用户有多个订单）
type User struct {
	gorm.Model
	Name   string `gorm:"not null"`
	Email  string `gorm:"uniqueIndex;not null"`
	Age    int
	Orders []Order `gorm:"foreignKey:UserID"` // 一对多关系
}

// Order 订单模型
type Order struct {
	gorm.Model
	OrderNo string  `gorm:"not null"`
	Amount  float64 `gorm:"not null"`
	UserID  uint    `gorm:"not null"`                                       // 外键
	User    User    `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"` // 反向引用
}

func GetSQLite() *gorm.DB {
	// 使用 modernc.org/sqlite 驱动（纯 Go 实现）
	db, err := gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        "uas2g.db",
	}, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 关闭日志打印
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	return db
}

func GetSQLiteWithAutoMigrate() *gorm.DB {
	// 使用 modernc.org/sqlite 驱动（纯 Go 实现）
	db, err := gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        "uas2g.db",
	}, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 关闭日志打印
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 自动迁移（创建表）
	if err := db.AutoMigrate(&User{}, &Order{}); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	return db
}

// NestedModelExample 嵌套模型示例
func NestedModelExample() {
	db := GetSQLiteWithAutoMigrate()

	// 1. 创建用户及其订单（一次性创建）
	user := User{
		Name:  "张三",
		Email: "<EMAIL>",
		Age:   25,
		Orders: []Order{
			{OrderNo: "ORD001", Amount: 100.50},
			{OrderNo: "ORD002", Amount: 200.75},
		},
	}

	// 创建用户和关联的订单
	result := db.Create(&user)
	if result.Error != nil {
		log.Printf("创建用户失败: %v", result.Error)
		return
	}
	fmt.Printf("创建用户成功，ID: %d，订单数量: %d\n", user.ID, len(user.Orders))

	// 2. 为已存在用户添加订单
	newOrder := Order{
		OrderNo: "ORD003",
		Amount:  150.25,
		UserID:  user.ID,
	}
	db.Create(&newOrder)
	fmt.Printf("为用户 %d 添加新订单: %s\n", user.ID, newOrder.OrderNo)

	// 3. 查询用户及其所有订单（预加载）
	var foundUser User
	db.Preload("Orders").First(&foundUser, user.ID)
	fmt.Printf("查询用户: %s，订单数量: %d\n", foundUser.Name, len(foundUser.Orders))
	for _, order := range foundUser.Orders {
		fmt.Printf("  订单: %s，金额: %.2f\n", order.OrderNo, order.Amount)
	}

	// 4. 条件查询用户的订单
	var expensiveOrders []Order
	db.Where("user_id = ? AND amount > ?", user.ID, 150).Find(&expensiveOrders)
	fmt.Printf("用户 %d 的高价值订单数量: %d\n", user.ID, len(expensiveOrders))

	// 5. 更新订单信息
	db.Model(&newOrder).Update("Amount", 180.00)
	fmt.Printf("订单 %s 金额已更新为: %.2f\n", newOrder.OrderNo, 180.00)

	// 6. 删除特定订单
	db.Delete(&newOrder)
	fmt.Printf("订单 %s 已删除\n", newOrder.OrderNo)

	// 7. 级联查询：通过订单查找用户
	var orderWithUser Order
	db.Preload("User").First(&orderWithUser, "order_no = ?", "ORD001")
	fmt.Printf("订单 %s 属于用户: %s\n", orderWithUser.OrderNo, orderWithUser.User.Name)

	// 8. 统计查询
	var orderCount int64
	db.Model(&Order{}).Where("user_id = ?", user.ID).Count(&orderCount)
	fmt.Printf("用户 %d 的订单总数: %d\n", user.ID, orderCount)

	// 9. 批量操作：删除用户的所有订单
	db.Where("user_id = ?", user.ID).Delete(&Order{})
	fmt.Printf("用户 %d 的所有订单已删除\n", user.ID)
}

// 使用你现有的 Account 和 Owner 模型示例
func AccountOwnerExample() {
	db := GetSQLiteWithAutoMigrate()

	// 迁移 Account 和 Owner 表
	db.AutoMigrate(&Account{}, &Owner{})

	// 创建账户及其所有者
	account := Account{
		AccountNo:   "ACC001",
		AccountName: "测试账户",
		AccountType: "个人账户",
		Status:      "active",
		Owners: []Owner{
			{
				Uid:    "U001",
				Name:   "张三",
				Gender: "男",
				Email:  "<EMAIL>",
			},
			{
				Uid:    "U002",
				Name:   "李四",
				Gender: "女",
				Email:  "<EMAIL>",
			},
		},
	}

	// 创建账户和关联的所有者
	result := db.Create(&account)
	if result.Error != nil {
		log.Printf("创建账户失败: %v", result.Error)
		return
	}
	fmt.Printf("创建账户成功: %s，所有者数量: %d\n", account.AccountNo, len(account.Owners))

	// 查询账户及其所有者
	var foundAccount Account
	db.Preload("Owners").First(&foundAccount, "account_no = ?", "ACC001")
	fmt.Printf("账户: %s，所有者:\n", foundAccount.AccountName)
	for _, owner := range foundAccount.Owners {
		fmt.Printf("  - %s (%s)\n", owner.Name, owner.Email)
	}
}

// UpsertExample 不存在则创建，存在则更新示例
func UpsertExample() {
	db := GetSQLiteWithAutoMigrate()

	// 方法1: 使用 FirstOrCreate - 基于条件查找，不存在则创建
	var user1 User
	result := db.Where(User{Email: "<EMAIL>"}).FirstOrCreate(&user1, User{
		Name:  "测试用户1",
		Email: "<EMAIL>",
		Age:   30,
	})
	if result.Error != nil {
		log.Printf("FirstOrCreate 失败: %v", result.Error)
		return
	}
	fmt.Printf("FirstOrCreate - 用户ID: %d, 是否新创建: %t\n", user1.ID, result.RowsAffected > 0)

	// 方法2: 使用 FirstOrInit + Save - 查找或初始化，然后保存
	var user2 User
	db.Where(User{Email: "<EMAIL>"}).FirstOrInit(&user2, User{
		Name:  "测试用户2",
		Email: "<EMAIL>",
		Age:   25,
	})
	// 可以在保存前修改字段
	user2.Age = 26
	db.Save(&user2)
	fmt.Printf("FirstOrInit + Save - 用户ID: %d, 年龄: %d\n", user2.ID, user2.Age)

	// 方法3: 使用 Clauses(clause.OnConflict) - 处理冲突时更新
	user3 := User{
		Name:  "测试用户3",
		Email: "<EMAIL>",
		Age:   28,
	}
	result = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "email"}},                                // 冲突字段
		DoUpdates: clause.AssignmentColumns([]string{"name", "age", "updated_at"}), // 更新字段
	}).Create(&user3)
	fmt.Printf("OnConflict - 用户ID: %d, 影响行数: %d\n", user3.ID, result.RowsAffected)

	// 方法4: 手动检查存在性
	var existingUser User
	err := db.Where("email = ?", "<EMAIL>").First(&existingUser).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 不存在，创建新记录
		newUser := User{
			Name:  "测试用户4",
			Email: "<EMAIL>",
			Age:   32,
		}
		db.Create(&newUser)
		fmt.Printf("手动检查 - 创建新用户ID: %d\n", newUser.ID)
	} else if err == nil {
		// 存在，更新记录
		db.Model(&existingUser).Updates(User{Name: "更新的用户4", Age: 33})
		fmt.Printf("手动检查 - 更新用户ID: %d\n", existingUser.ID)
	}

	// 方法5: 批量 Upsert
	users := []User{
		{Name: "批量用户1", Email: "<EMAIL>", Age: 20},
		{Name: "批量用户2", Email: "<EMAIL>", Age: 21},
		{Name: "批量用户3", Email: "<EMAIL>", Age: 22},
	}

	result = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "email"}},
		DoUpdates: clause.AssignmentColumns([]string{"name", "age", "updated_at"}),
	}).Create(&users)
	fmt.Printf("批量 Upsert - 影响行数: %d\n", result.RowsAffected)
}

// NestedUpsertExample 嵌套模型的 Upsert 示例
func NestedUpsertExample() {
	db := GetSQLiteWithAutoMigrate()

	// 用户和订单的 Upsert
	var user User
	db.Where(User{Email: "<EMAIL>"}).FirstOrCreate(&user, User{
		Name:  "嵌套用户",
		Email: "<EMAIL>",
		Age:   35,
	})

	// 为用户添加或更新订单
	orders := []Order{
		{OrderNo: "NEST001", Amount: 100.0, UserID: user.ID},
		{OrderNo: "NEST002", Amount: 200.0, UserID: user.ID},
	}

	for _, order := range orders {
		var existingOrder Order
		err := db.Where("order_no = ? AND user_id = ?", order.OrderNo, order.UserID).First(&existingOrder).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 订单不存在，创建新订单
			db.Create(&order)
			fmt.Printf("创建新订单: %s, 金额: %.2f\n", order.OrderNo, order.Amount)
		} else if err == nil {
			// 订单存在，更新金额
			db.Model(&existingOrder).Update("Amount", order.Amount+50.0)
			fmt.Printf("更新订单: %s, 新金额: %.2f\n", existingOrder.OrderNo, order.Amount+50.0)
		}
	}

	// 查询结果
	var foundUser User
	db.Preload("Orders").First(&foundUser, user.ID)
	fmt.Printf("用户 %s 的订单数量: %d\n", foundUser.Name, len(foundUser.Orders))
}
