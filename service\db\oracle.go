package db

func TestOracle() {
	// // 连接字符串格式: user/pass@host:port/service_name
	// // 例如: "scott/tiger@localhost:1521/ORCLPDB1"
	// db, err := sql.Open("godror", "user/pass@localhost:1521/ORCLPDB1")
	// if err != nil {
	// 	log.Fatal(err)
	// }
	// defer db.Close()

	// // 测试连接
	// err = db.<PERSON>()
	// if err != nil {
	// 	log.Fatal(err)
	// }
	// fmt.Println("成功连接到Oracle数据库")

	// // 查询示例
	// rows, err := db.Query("SELECT empno, ename FROM emp WHERE deptno = :1", 10)
	// if err != nil {
	// 	log.Fatal(err)
	// }
	// defer rows.Close()

	// for rows.Next() {
	// 	var empno int
	// 	var ename string
	// 	err = rows.Scan(&empno, &ename)
	// 	if err != nil {
	// 		log.Fatal(err)
	// 	}
	// 	fmt.Printf("员工号: %d, 姓名: %s\n", empno, ename)
	// }

	// // 插入示例
	// result, err := db.Exec("INSERT INTO emp(empno, ename) VALUES(:1, :2)", 9999, "GOUSER")
	// if err != nil {
	// 	log.Fatal(err)
	// }
	// rowsAffected, err := result.RowsAffected()
	// fmt.Printf("插入了 %d 行\n", rowsAffected)
}
