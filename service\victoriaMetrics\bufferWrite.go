package victoriaMetrics

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"sync"
	"time"

	"github.com/VictoriaMetrics/metrics"
)

type MetricBuffer struct {
	url           string        // 目标VictoriaMetrics的URL
	maxSize       int           // 缓冲区大小
	flushInterval time.Duration // 刷新间隔
	mu            sync.Mutex    // 互斥锁
	buffer        []string      // 缓冲区
	stopped       chan struct{} // 停止信号
	set           *metrics.Set  // VictoriaMetrics metrics set
}

// 创建新的MetricBuffer实例
func NewMetricBuffer(url string, maxSize int, flushInterval time.Duration) *MetricBuffer {
	mb := &MetricBuffer{
		url:           url,
		maxSize:       maxSize,
		flushInterval: flushInterval,
		buffer:        make([]string, 0, maxSize),
		stopped:       make(chan struct{}),
		set:           metrics.NewSet(),
	}

	// 启动后台刷新协程
	go mb.backgroundFlush()
	return mb
}

// 后台刷新协程
func (mb *MetricBuffer) backgroundFlush() {
	// 创建定时器
	ticker := time.NewTicker(mb.flushInterval)
	defer ticker.Stop()
	// 循环
	for {
		// 选择定时器或停止信号
		select {
		case <-ticker.C:
			// 加锁
			mb.mu.Lock()
			// 如果缓冲区有数据，则刷新
			if len(mb.buffer) > 0 {
				mb.flush()
			}
			mb.mu.Unlock()
		case <-mb.stopped:
			return
		}
	}
}

// 刷新缓冲区
func (mb *MetricBuffer) flush() {
	slog.Info(fmt.Sprintf("before send buffer size:%v", len(mb.buffer)))

	// 创建一个新的metrics set用于批量写入
	set := metrics.NewSet()

	// 将缓冲区中的指标添加到set中
	for _, metricStr := range mb.buffer {
		// 解析指标字符串
		var metric struct {
			Name      string            `json:"name"`
			Value     float64           `json:"value"`
			Labels    map[string]string `json:"labels"`
			Timestamp int64             `json:"timestamp"`
		}

		if err := json.Unmarshal([]byte(metricStr), &metric); err != nil {
			slog.Error("Failed to unmarshal metric", "error", err)
			continue
		}

		// 创建指标名称（包含标签）
		metricName := metric.Name
		if len(metric.Labels) > 0 {
			labels := make([]string, 0, len(metric.Labels))
			for k, v := range metric.Labels {
				labels = append(labels, fmt.Sprintf("%s=%q", k, v))
			}
			metricName = fmt.Sprintf("%s{%s}", metric.Name, strings.Join(labels, ","))
		}

		// 创建并注册指标
		gauge := set.NewGauge(metricName, nil)
		gauge.Set(metric.Value)
	}

	// 推送指标到VictoriaMetrics
	if err := set.PushMetrics(context.TODO(), mb.url, nil); err != nil {
		slog.Error("Failed to push metrics", "error", err)
		// TODO: implement retry logic
		return
	}

	slog.Info(fmt.Sprintf("Successfully pushed %d metrics", len(mb.buffer)))
	mb.buffer = mb.buffer[:0] // 清空缓冲区
}

// 停止缓冲区
func (mb *MetricBuffer) Stop() {
	// 关闭停止信号
	close(mb.stopped)
	// 加锁
	mb.mu.Lock()
	defer mb.mu.Unlock()

	// 如果缓冲区有数据，则刷新
	if len(mb.buffer) > 0 {
		mb.flush()
	}
}

// 添加指标到缓冲区
func (mb *MetricBuffer) AddMetric(name string, value float64, labels map[string]string, timestamp int64) {
	// 创建指标对象
	metric := struct {
		Name      string            `json:"name"`
		Value     float64           `json:"value"`
		Labels    map[string]string `json:"labels"`
		Timestamp int64             `json:"timestamp"`
	}{
		Name:      name,
		Value:     value,
		Labels:    labels,
		Timestamp: timestamp,
	}

	// 序列化指标
	metricBytes, err := json.Marshal(metric)
	if err != nil {
		slog.Error("Failed to marshal metric", "error", err)
		return
	}

	// 加锁
	mb.mu.Lock()
	defer mb.mu.Unlock()

	// 添加到缓冲区
	mb.buffer = append(mb.buffer, string(metricBytes))

	// 如果缓冲区大小达到最大值，则刷新
	if len(mb.buffer) >= mb.maxSize {
		mb.flush()
	}
}
