package monitor

import (
	"fmt"
	"runtime"
	"testing"
	"time"

	"github.com/shirou/gopsutil/v3/process"
)



// BenchmarkGetProcessInfoWithGopsutil 基准测试 - 使用gopsutil获取进程信息
func BenchmarkGetProcessInfoWithGopsutil(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := GetProcessInfoWithGopsutil()
		if err != nil {
			b.<PERSON><PERSON>(err)
		}
	}
}

// BenchmarkGetProcessInfoDetail 基准测试 - 获取单个进程详细信息
func BenchmarkGetProcessInfoDetail(b *testing.B) {
	// 获取第一个进程用于测试
	processes, err := process.Processes()
	if err != nil || len(processes) == 0 {
		b.<PERSON>p("无法获取进程列表")
	}

	testProcess := processes[0]

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := getProcessInfoDetailWithGopsutil(testProcess)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// TestGetProcessInfoWithGopsutil 功能测试
func TestGetProcessInfoWithGopsutil(t *testing.T) {
	processes, err := GetProcessInfoWithGopsutil()
	if err != nil {
		t.Fatalf("获取进程信息失败: %v", err)
	}

	if len(processes) == 0 {
		t.Error("未获取到任何进程信息")
	}

	// 验证进程信息的基本字段
	for i, proc := range processes {
		if i >= 5 { // 只检查前5个进程
			break
		}

		if proc.PID < 0 {
			t.Errorf("进程 %d PID 无效: %d", i, proc.PID)
		}

		if proc.Name == "" {
			t.Errorf("进程PID:%d 名称为空,", proc.PID)
		}
	}
}

// CompareProcessInfoPerformance 性能对比测试
func CompareProcessInfoPerformance() {
	fmt.Println("=== 进程信息获取性能测试 ===")
	fmt.Printf("运行环境: %s/%s, CPU核心数: %d\n", runtime.GOOS, runtime.GOARCH, runtime.NumCPU())
	fmt.Println()

	// 测试获取进程信息
	fmt.Println("测试 GetProcessInfoWithGopsutil...")
	start := time.Now()
	processes, err := GetProcessInfoWithGopsutil()
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("获取进程信息失败: %v\n", err)
		return
	}

	fmt.Printf("获取 %d 个进程信息，耗时: %v\n", len(processes), duration)
	fmt.Printf("平均每个进程耗时: %v\n", duration/time.Duration(len(processes)))
}
