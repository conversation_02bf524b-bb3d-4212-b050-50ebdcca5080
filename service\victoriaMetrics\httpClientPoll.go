package victoriaMetrics

import (
	"net/http"
	"sync"
	"time"
)

type HttpClientPool struct {
	clients []*http.Client
	mu      sync.Mutex
	index   int
}

func NewHttpClientPool(size int, timeout time.Duration) *HttpClientPool {
	pool := &HttpClientPool{
		clients: make([]*http.Client, size),
	}

	for i := 0; i < size; i++ {

		pool.clients[i] = &http.Client{
			Timeout: timeout,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
			},
		}
	}

	return pool
}

func (p *HttpClientPool) Get() *http.Client {
	p.mu.Lock()
	defer p.mu.Unlock()

	client := p.clients[p.index]
	p.index = (p.index + 1) % len(p.clients)
	return client
}
