package localLogs

import (
	"fmt"
	"log"
	"os"
	"victoriaMetricsCollector/utils"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// DefaultZapLogConfig 返回默认的日志配置
func DefaultZapLogConfig() ZapLogConfig {
	return ZapLogConfig{
		LogLevel:      "info",
		LogDir:        "logs",
		LogFileName:   "app.log",
		LogMaxSize:    10,
		LogMaxAge:     7,
		LogMaxBackups: 3,
	}
}

// NewZapLogWithDefaults 使用默认配置创建日志记录器
func NewZapLogWithDefaults() *zap.Logger {
	return NewZapLog(DefaultZapLogConfig())
}

// ZapLogConfig Zap日志配置结构体 ========================================>
// 包含了日志系统的所有配置参数
type ZapLogConfig struct {
	LogLevel          string // 日志级别 (debug, info, warn, error)
	IsDynamicLogLevel bool   // 是否支持动态调整日志级别
	LogDir            string // 日志文件存储目录
	LogFileName       string // 日志文件名称
	LogMaxSize        int    // 单个日志文件最大大小 (MB)
	LogMaxAge         int    // 日志文件保留天数
	LogMaxBackups     int    // 最大保留的旧日志文件数量
}

// ValidateConfig 验证日志配置参数的有效性
func (config *ZapLogConfig) ValidateConfig() error {
	if config.LogDir == "" {
		return fmt.Errorf("日志目录不能为空")
	}
	if config.LogFileName == "" {
		return fmt.Errorf("日志文件名不能为空")
	}
	if config.LogMaxSize <= 0 {
		return fmt.Errorf("日志文件最大大小必须大于0")
	}
	if config.LogMaxAge <= 0 {
		return fmt.Errorf("日志文件保留天数必须大于0")
	}
	if config.LogMaxBackups < 0 {
		return fmt.Errorf("日志文件备份数量不能为负数")
	}

	validLevels := []string{"debug", "info", "warn", "error"}
	for _, level := range validLevels {
		if config.LogLevel == level {
			return nil
		}
	}
	return fmt.Errorf("无效的日志级别: %s，支持的级别: %v", config.LogLevel, validLevels)
}

// NewZapLog 创建并配置一个新的Zap日志记录器
// 该函数会创建一个同时输出到控制台和文件的日志记录器
//
// 参数:
//
//	zapConfig: 日志配置参数
//
// 返回:
//
//	*zap.Logger: 配置好的日志记录器实例
func NewZapLog(zapConfig ZapLogConfig) *zap.Logger {

	// 配置标准输出 (控制台输出)
	// 将日志同时输出到控制台，便于开发调试
	stdout := zapcore.AddSync(os.Stdout)

	// 确保日志目录存在
	// 如果目录不存在则创建，避免写入文件时出错
	err := utils.EnsureDirExists(zapConfig.LogDir)
	if err != nil {
		log.Fatal(err)
	}

	// 配置文件输出，使用lumberjack库进行日志文件的自动切分和管理
	// lumberjack可以自动处理日志文件的轮转、压缩和清理
	lg := lumberjack.Logger{
		Filename:   zapConfig.LogDir + "/" + zapConfig.LogFileName, // 日志文件完整路径
		MaxSize:    zapConfig.LogMaxSize,                           // 单个文件最大大小(MB)，超过后自动切分
		MaxBackups: zapConfig.LogMaxBackups,                        // 保留的旧文件数量，超过后删除最旧的
		MaxAge:     zapConfig.LogMaxAge,                            // 文件保留天数，超过后自动删除
		LocalTime:  true,                                           // 使用本地时间命名备份文件
	}

	// 将lumberjack logger包装为zapcore可用的WriteSyncer
	file := zapcore.AddSync(&lg)

	// 设置日志记录级别，默认为info级别
	// 只有等于或高于设置级别的日志才会被记录
	level := zap.NewAtomicLevelAt(zap.InfoLevel)

	// 根据配置字符串设置具体的日志级别
	switch zapConfig.LogLevel {
	case "debug":
		level = zap.NewAtomicLevelAt(zap.DebugLevel) // 最详细，包含调试信息
	case "warn":
		level = zap.NewAtomicLevelAt(zap.WarnLevel) // 警告级别
	case "error":
		level = zap.NewAtomicLevelAt(zap.ErrorLevel) // 只记录错误
		// 默认情况使用info级别
	}

	// 配置生产环境的编码器配置
	// 用于文件输出，采用JSON格式便于日志分析工具处理
	productionCfg := zap.NewProductionEncoderConfig()
	productionCfg.TimeKey = "datetime"                    // 时间字段名
	productionCfg.EncodeTime = zapcore.RFC3339TimeEncoder // 时间格式：RFC3339标准

	// 配置开发环境的编码器配置
	// 用于控制台输出，更易于人类阅读
	developmentCfg := zap.NewDevelopmentEncoderConfig()
	developmentCfg.EncodeLevel = zapcore.CapitalColorLevelEncoder // 彩色大写级别显示
	developmentCfg.EncodeTime = zapcore.RFC3339TimeEncoder        // 时间格式：RFC3339标准

	// 创建控制台编码器 - 用于终端输出
	// 使用开发配置，输出格式更适合人类阅读，支持颜色
	consoleEncoder := zapcore.NewConsoleEncoder(developmentCfg)

	// 创建文件编码器 - 用于文件输出
	// 使用生产配置，输出JSON格式便于程序解析和日志分析
	fileEncoder := zapcore.NewJSONEncoder(productionCfg)

	// 创建多输出核心 (Tee)
	// 同时将日志输出到控制台和文件，使用不同的编码器
	core := zapcore.NewTee(
		zapcore.NewCore(consoleEncoder, stdout, level), // 控制台输出核心
		zapcore.NewCore(fileEncoder, file, level),      // 文件输出核心
	)

	// 创建最终的logger实例
	// AddStacktrace(zap.ErrorLevel): 在错误级别及以上自动添加堆栈跟踪信息
	zapLog := zap.New(core, zap.AddStacktrace(zap.ErrorLevel))

	return zapLog
}

// LoggerManager 日志管理器，支持动态调整日志级别
// 相比于普通的 zap.Logger，LoggerManager 提供了以下增强功能：
// 1. 运行时动态调整日志级别，无需重启应用
// 2. 保持对 AtomicLevel 的引用，实现线程安全的级别切换
// 3. 封装了日志器的创建和管理逻辑
type LoggerManager struct {
	logger *zap.Logger     // 实际的日志记录器实例
	level  zap.AtomicLevel // 原子级别控制器，支持并发安全的级别调整
}

// NewLoggerManager 创建可管理的日志记录器
func NewLoggerManager(zapConfig ZapLogConfig) (*LoggerManager, error) {
	// 第一步：配置验证 - 确保传入的配置参数有效
	if err := zapConfig.ValidateConfig(); err != nil {
		return nil, err
	}

	// 配置标准输出 (控制台输出)
	// 将日志同时输出到控制台，便于开发调试
	stdout := zapcore.AddSync(os.Stdout)

	// 确保日志目录存在
	// 如果目录不存在则创建，避免写入文件时出错
	err := utils.EnsureDirExists(zapConfig.LogDir)
	if err != nil {
		log.Fatal(err)
	}

	// 配置文件输出，使用lumberjack库进行日志文件的自动切分和管理
	// lumberjack可以自动处理日志文件的轮转、压缩和清理
	lg := lumberjack.Logger{
		Filename:   zapConfig.LogDir + "/" + zapConfig.LogFileName, // 日志文件完整路径
		MaxSize:    zapConfig.LogMaxSize,                           // 单个文件最大大小(MB)，超过后自动切分
		MaxBackups: zapConfig.LogMaxBackups,                        // 保留的旧文件数量，超过后删除最旧的
		MaxAge:     zapConfig.LogMaxAge,                            // 文件保留天数，超过后自动删除
		LocalTime:  true,                                           // 使用本地时间命名备份文件
	}

	// 将lumberjack logger包装为zapcore可用的WriteSyncer
	file := zapcore.AddSync(&lg)

	// 第二步：创建 AtomicLevel - 这是动态调整的关键
	// 设置日志记录级别，默认为info级别
	// 只有等于或高于设置级别的日志才会被记录
	level := zap.NewAtomicLevelAt(zap.InfoLevel)

	// 根据配置字符串设置具体的日志级别
	switch zapConfig.LogLevel {
	case "debug":
		level = zap.NewAtomicLevelAt(zap.DebugLevel) // 最详细，包含调试信息
	case "warn":
		level = zap.NewAtomicLevelAt(zap.WarnLevel) // 警告级别
	case "error":
		level = zap.NewAtomicLevelAt(zap.ErrorLevel) // 只记录错误
		// 默认情况使用info级别
	}

	// 配置生产环境的编码器配置
	// 用于文件输出，采用JSON格式便于日志分析工具处理
	productionCfg := zap.NewProductionEncoderConfig()
	productionCfg.TimeKey = "datetime"                    // 时间字段名
	productionCfg.EncodeTime = zapcore.RFC3339TimeEncoder // 时间格式：RFC3339标准

	// 配置开发环境的编码器配置
	// 用于控制台输出，更易于人类阅读
	developmentCfg := zap.NewDevelopmentEncoderConfig()
	developmentCfg.EncodeLevel = zapcore.CapitalColorLevelEncoder // 彩色大写级别显示
	developmentCfg.EncodeTime = zapcore.RFC3339TimeEncoder        // 时间格式：RFC3339标准

	// 创建控制台编码器 - 用于终端输出
	// 使用开发配置，输出格式更适合人类阅读，支持颜色
	consoleEncoder := zapcore.NewConsoleEncoder(developmentCfg)

	// 创建文件编码器 - 用于文件输出
	// 使用生产配置，输出JSON格式便于程序解析和日志分析
	fileEncoder := zapcore.NewJSONEncoder(productionCfg)

	// 第四步：创建 Core，将 level 传入两个输出核心
	// 创建多输出核心 (Tee)
	// 同时将日志输出到控制台和文件，使用不同的编码器
	core := zapcore.NewTee(
		zapcore.NewCore(consoleEncoder, stdout, level), // 控制台输出核心
		zapcore.NewCore(fileEncoder, file, level),      // 文件输出核心
	)

	// 第五步：创建 logger 并返回管理器
	// 创建最终的logger实例
	// AddStacktrace(zap.ErrorLevel): 在错误级别及以上自动添加堆栈跟踪信息
	zapLog := zap.New(core, zap.AddStacktrace(zap.ErrorLevel))

	return &LoggerManager{
		logger: zapLog,
		level:  level,
	}, nil
}

// SetLevel 动态调整日志级别
func (lm *LoggerManager) SetLevel(levelStr string) error {
	switch levelStr {
	case "debug":
		lm.level.SetLevel(zap.DebugLevel)
	case "info":
		lm.level.SetLevel(zap.InfoLevel)
	case "warn":
		lm.level.SetLevel(zap.WarnLevel)
	case "error":
		lm.level.SetLevel(zap.ErrorLevel)
	default:
		return fmt.Errorf("无效的日志级别: %s", levelStr)
	}
	return nil
}

// GetLogger 获取日志记录器
func (lm *LoggerManager) GetLogger() *zap.Logger {
	return lm.logger
}
