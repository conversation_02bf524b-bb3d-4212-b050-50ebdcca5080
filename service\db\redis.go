package db

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
)

func TestRedis() {
	// 初始化Redis客户端
	rc := NewRedisClient("11.11.11.11:6371", "", 0)
	defer rc.Close()

	// 测试连接
	if _, err := rc.Ping(); err != nil {
		log.Fatalf("无法连接Redis: %v", err)
	}
	fmt.Println("Redis连接成功")

	// 字符串操作示例
	if err := rc.Set("username", "john_doe", 10*time.Minute); err != nil {
		log.Printf("设置键失败: %v", err)
	}

	val, err := rc.Get("username")
	if err != nil {
		log.Printf("获取键失败: %v", err)
	} else {
		fmt.Printf("用户名: %s\n", val)
	}

	// 哈希操作示例
	userData := map[string]interface{}{
		"name":  "<PERSON>",
		"email": "<EMAIL>",
		"age":   30,
	}
	if err := rc.HSet("user:1001", userData); err != nil {
		log.Printf("设置哈希失败: %v", err)
	}

	email, err := rc.HGet("user:1001", "email")
	if err != nil {
		log.Printf("获取哈希字段失败: %v", err)
	} else {
		fmt.Printf("用户邮箱: %s\n", email)
	}

	// 列表操作示例
	if err := rc.LPush("messages", "hello", "world"); err != nil {
		log.Printf("列表操作失败: %v", err)
	}

	messages, err := rc.LRange("messages", 0, -1)
	if err != nil {
		log.Printf("获取列表失败: %v", err)
	} else {
		fmt.Printf("消息列表: %v\n", messages)
	}

	// 集合操作示例
	if err := rc.SAdd("tags", "golang", "redis", "database"); err != nil {
		log.Printf("集合操作失败: %v", err)
	}

	tags, err := rc.SMembers("tags")
	if err != nil {
		log.Printf("获取集合失败: %v", err)
	} else {
		fmt.Printf("标签集合: %v\n", tags)
	}

	// 有序集合操作示例
	if err := rc.ZAdd("leaderboard", redis.Z{Score: 100, Member: "player1"}, redis.Z{Score: 200, Member: "player2"}); err != nil {
		log.Printf("有序集合操作失败: %v", err)
	}

	players, err := rc.ZRevRange("leaderboard", 0, -1)
	if err != nil {
		log.Printf("获取有序集合失败: %v", err)
	} else {
		fmt.Printf("排行榜: %v\n", players)
	}
}

// RedisClient 封装Redis操作的结构体
type RedisClient struct {
	client *redis.Client
	ctx    context.Context
}

// NewRedisClient 创建新的RedisClient实例
func NewRedisClient(addr, password string, db int) *RedisClient {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	return &RedisClient{
		client: client,
		ctx:    context.Background(),
	}
}

// Close 关闭Redis连接
func (rc *RedisClient) Close() error {
	return rc.client.Close()
}

// Ping 测试连接
func (rc *RedisClient) Ping() (string, error) {
	return rc.client.Ping(rc.ctx).Result()
}

// --- 字符串操作 ---

// Set 设置字符串键值
func (rc *RedisClient) Set(key string, value interface{}, expiration time.Duration) error {
	return rc.client.Set(rc.ctx, key, value, expiration).Err()
}

// Get 获取字符串值
func (rc *RedisClient) Get(key string) (string, error) {
	return rc.client.Get(rc.ctx, key).Result()
}

// --- 哈希操作 ---

// HSet 设置哈希字段
func (rc *RedisClient) HSet(key string, values map[string]interface{}) error {
	return rc.client.HSet(rc.ctx, key, values).Err()
}

// HGet 获取哈希字段值
func (rc *RedisClient) HGet(key, field string) (string, error) {
	return rc.client.HGet(rc.ctx, key, field).Result()
}

// HGetAll 获取所有哈希字段
func (rc *RedisClient) HGetAll(key string) (map[string]string, error) {
	return rc.client.HGetAll(rc.ctx, key).Result()
}

// --- 列表操作 ---

// LPush 从列表左侧插入
func (rc *RedisClient) LPush(key string, values ...interface{}) error {
	return rc.client.LPush(rc.ctx, key, values...).Err()
}

// RPush 从列表右侧插入
func (rc *RedisClient) RPush(key string, values ...interface{}) error {
	return rc.client.RPush(rc.ctx, key, values...).Err()
}

// LRange 获取列表范围
func (rc *RedisClient) LRange(key string, start, stop int64) ([]string, error) {
	return rc.client.LRange(rc.ctx, key, start, stop).Result()
}

// --- 集合操作 ---

// SAdd 向集合添加元素
func (rc *RedisClient) SAdd(key string, members ...interface{}) error {
	return rc.client.SAdd(rc.ctx, key, members...).Err()
}

// SMembers 获取集合所有成员
func (rc *RedisClient) SMembers(key string) ([]string, error) {
	return rc.client.SMembers(rc.ctx, key).Result()
}

// --- 有序集合操作 ---

// ZAdd 向有序集合添加成员
func (rc *RedisClient) ZAdd(key string, members ...redis.Z) error {
	return rc.client.ZAdd(rc.ctx, key, members...).Err()
}

// ZRange 按分数从低到高获取有序集合成员
func (rc *RedisClient) ZRange(key string, start, stop int64) ([]string, error) {
	return rc.client.ZRange(rc.ctx, key, start, stop).Result()
}

// ZRevRange 按分数从高到低获取有序集合成员
func (rc *RedisClient) ZRevRange(key string, start, stop int64) ([]string, error) {
	return rc.client.ZRevRange(rc.ctx, key, start, stop).Result()
}

// --- 事务操作 ---

// TxPipeline 开始事务
func (rc *RedisClient) TxPipeline() redis.Pipeliner {
	return rc.client.TxPipeline()
}

// --- 发布/订阅操作 ---

// Publish 发布消息
func (rc *RedisClient) Publish(channel string, message interface{}) error {
	return rc.client.Publish(rc.ctx, channel, message).Err()
}

// Subscribe 订阅频道
func (rc *RedisClient) Subscribe(channels ...string) *redis.PubSub {
	return rc.client.Subscribe(rc.ctx, channels...)
}
