package prometheus

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

var (
	healthyState = true
)

func RootPage(w http.ResponseWriter, r *http.Request) {

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprintf(w, `
			<!DOCTYPE html>
			<html>
			<head>
				<title>Prometheus Exporter</title>
				<style>
					body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
					.menu {
						display: flex;
						justify-content: center;
						gap: 20px;
						margin-top: 30px;
					}
					a {
						padding: 10px 20px;
						background-color: #4CAF50;
						color: white;
						text-decoration: none;
						border-radius: 5px;
					}
					a:hover { background-color: #45a049; }
				</style>
			</head>
			<body>
				<h1>Prometheus Exporter</h1>
				<p>Available endpoints:</p>
				<div class="menu">
					<a href="/metrics">Metrics</a>
					<a href="/health">Health Check</a>
				</div>
			</body>
			</html>
		`)
}

// HealthResponse 定义健康检查响应的结构
type HealthResponse struct {
	Status    string  `json:"status"`
	Uptime    float64 `json:"uptime_seconds"`
	Timestamp string  `json:"timestamp"`
}

func HealthCheck(w http.ResponseWriter, r *http.Request) {
	uptime := time.Since(time.Now()).Seconds()
	response := HealthResponse{
		Status:    "healthy",
		Uptime:    uptime,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	if !healthyState {
		response.Status = "unhealthy"
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	w.Header().Set("Content-Type", "application/json")
	err := json.NewEncoder(w).Encode(response)
	if err != nil {
		log.Printf("Failed to encode health response: %v", err)
	}
}
