package monitor

import (
	"fmt"
	"os/user"
	"runtime"
	"strconv"
	"time"

	"github.com/elastic/go-sysinfo"
	"github.com/elastic/go-sysinfo/types"
	"github.com/shirou/gopsutil/v3/process"
)

type ProcessInfo struct {
	// Name            string   // 进程名称
	// PID             int      // 进程ID
	// PPID            int      // 父进程ID
	// Cmdline         string   // 命令行参数
	// StartTime       string   // 启动时间
	// ExePath         string   // 可执行文件路径
	// UID             string   // 用户ID
	// Username        string   // 用户名
	// State           string // 进程状态
	// ThreadCount     int    // 线程数
	// FileDescriptors int    // 文件描述符数量
	ProcessBasic
	// RSS             uint64   // 常驻内存(RSS)
	// VMS             uint64   // 虚拟内存(VMS)
	// CPUUser         float64  // 用户态CPU时间
	// CPUSystem       float64  // 系统态CPU时间
	ProcessResource
	// IOReadCount    uint64   // IO读取次数
	// IOWriteCount   uint64   // IO写入次数
	// IOReadBytes    uint64   // IO读取字节数
	// IOWriteBytes   uint64   // IO写入字节数
	ProcessDisk
	// NetConnections int      // 网络连接数
	// TCPPorts       []uint16 // TCP监听端口
	// UDPPorts       []uint16 // UDP监听端口
	ProcessNet
}

type ProcessBasic struct {
	Name            string // 进程名称
	PID             int    // 进程ID
	PPID            int    // 父进程ID
	Cmdline         string // 命令行参数
	StartTime       string // 启动时间
	ExePath         string // 可执行文件路径
	UID             string // 用户ID
	Username        string // 用户名
	State           string // 进程状态
	ThreadCount     int    // 线程数
	FileDescriptors int    // 文件描述符数量
}

type ProcessResource struct {
	RSS        uint64  // 常驻内存(RSS)
	VMS        uint64  // 虚拟内存(VMS)
	CPUUser    float64 // 用户态CPU时间
	CPUSystem  float64 // 系统态CPU时间
	CPUPercent float64 // CPU使用率
}

type ProcessDisk struct {
	IOReadCount  uint64 // IO读取次数
	IOWriteCount uint64 // IO写入次数
	IOReadBytes  uint64 // IO读取字节数
	IOWriteBytes uint64 // IO写入字节数
}

type ProcessNet struct {
	NetConnections int      // 网络连接数
	TCPPorts       []uint16 // TCP监听端口
	UDPPorts       []uint16 // UDP监听端口
}

func Run() {
	// 性能测试对比
	performanceTest("gopsutil", GetProcessInfoWithGopsutil)
	time.Sleep(1 * time.Second)
	performanceTest("sysinfo", GetProcessInfoWithSysinfo)
}

// performanceTest 通用性能测试方法
func performanceTest(name string, testFunc func() ([]ProcessInfo, error)) {
	fmt.Printf("测试开始 - %s...\n", name)
	runtime.GC()

	// 获取执行前的内存和GC统计
	var memBefore runtime.MemStats
	runtime.ReadMemStats(&memBefore)
	goroutinesBefore := runtime.NumGoroutine()

	start := time.Now()

	// 执行测试函数
	result, err := testFunc()

	duration := time.Since(start)

	// 获取执行后的内存和GC统计
	var memAfter runtime.MemStats
	runtime.ReadMemStats(&memAfter)
	goroutinesAfter := runtime.NumGoroutine()

	if err != nil {
		fmt.Printf("%s 测试失败: %v\n", name, err)
	} else {
		fmt.Printf("%s 获取到 %d 个进程\n", name, len(result))
	}

	// 计算资源消耗
	var memAllocDiff, memSysDiff int64

	// 安全计算内存差异，避免uint64下溢
	if memAfter.Alloc >= memBefore.Alloc {
		memAllocDiff = int64(memAfter.Alloc - memBefore.Alloc)
	} else {
		memAllocDiff = -int64(memBefore.Alloc - memAfter.Alloc)
	}

	if memAfter.Sys >= memBefore.Sys {
		memSysDiff = int64(memAfter.Sys - memBefore.Sys)
	} else {
		memSysDiff = -int64(memBefore.Sys - memAfter.Sys)
	}

	gcDiff := memAfter.NumGC - memBefore.NumGC
	goroutinesDiff := goroutinesAfter - goroutinesBefore

	fmt.Printf("%s 耗时: %v\n", name, duration)
	fmt.Printf("%s 内存分配: %d bytes (%.2f KB)\n", name, memAllocDiff, float64(memAllocDiff)/1024)
	fmt.Printf("%s 系统内存: %d bytes (%.2f KB)\n", name, memSysDiff, float64(memSysDiff)/1024)
	fmt.Printf("%s GC次数: %d\n", name, gcDiff)
	fmt.Printf("%s Goroutine变化: %d\n", name, goroutinesDiff)
	fmt.Printf("%s 当前Goroutine数: %d\n", name, goroutinesAfter)
	fmt.Println("===========================================")

	runtime.GC()

	time.Sleep(time.Second * 1)
}

// GetProcessInfoWithGopsutil 使用gopsutil高效获取进程信息
func GetProcessInfoWithGopsutil() ([]ProcessInfo, error) {
	processes, err := process.Processes()
	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	var result []ProcessInfo
	for _, p := range processes {
		info, err := getProcessInfoDetailWithGopsutil(p)
		if err != nil {
			// 记录错误但继续处理其他进程
			continue
		}

		result = append(result, info)
	}

	// 打印结果统计
	fmt.Printf("获取到 %d 个进程信息:\n", len(result))

	// 打印前几个进程的详细信息
	// for i, proc := range result {
	// 	// if i >= 5 { // 只打印前5个进程，避免输出过多
	// 	// 	break
	// 	// }

	// 	fmt.Printf("进程 %d:\n", i+1)
	// 	fmt.Printf("  PID=%d, PPID=%d, 名称=%s\n", proc.PID, proc.PPID, proc.Name)
	// 	fmt.Printf("  用户=%s(UID:%s), 状态=%s\n", proc.Username, proc.UID, proc.State)
	// 	fmt.Printf("  内存: RSS=%d bytes, VMS=%d bytes\n", proc.RSS, proc.VMS)
	// 	fmt.Printf("  CPU: 用户态=%.2fs, 系统态=%.2fs\n", proc.CPUUser, proc.CPUSystem)
	// 	fmt.Printf("  线程数=%d, 文件描述符=%d\n", proc.ThreadCount, proc.FileDescriptors)
	// 	fmt.Printf("  网络连接数=%d\n", proc.NetConnections)
	// 	if len(proc.TCPPorts) > 0 {
	// 		fmt.Printf("  TCP端口: %v\n", proc.TCPPorts)
	// 	}
	// 	if len(proc.UDPPorts) > 0 {
	// 		fmt.Printf("  UDP端口: %v\n", proc.UDPPorts)
	// 	}
	// 	fmt.Printf("  启动时间=%s\n", proc.StartTime)
	// 	fmt.Printf("  执行路径=%s\n", proc.ExePath)
	// 	fmt.Printf("  命令行=%s\n", proc.Cmdline)

	// 	fmt.Println("  ----------------------------------------")
	// }

	return result, nil
}

// getProcessInfoDetailWithGopsutil 获取单个进程的详细信息
func getProcessInfoDetailWithGopsutil(p *process.Process) (ProcessInfo, error) {
	var info ProcessInfo

	// 基本信息
	info.PID = int(p.Pid)

	// 进程名称
	if name, err := p.Name(); err == nil {
		info.Name = name
	}

	// 父进程ID
	if ppid, err := p.Ppid(); err == nil {
		info.PPID = int(ppid)
	}

	// 命令行参数
	if cmdline, err := p.Cmdline(); err == nil {
		info.Cmdline = cmdline
	}

	// 启动时间
	if createTime, err := p.CreateTime(); err == nil {
		info.StartTime = time.Unix(createTime/1000, 0).Format("2006-01-02 15:04:05")
	}

	// 可执行文件路径
	if exe, err := p.Exe(); err == nil {
		info.ExePath = exe
	}

	// 用户信息
	if uids, err := p.Uids(); err == nil && len(uids) > 0 {
		info.UID = strconv.Itoa(int(uids[0]))
		if u, err := user.LookupId(info.UID); err == nil {
			info.Username = u.Username
		}
	}

	// 进程状态
	if statuses, err := p.Status(); err == nil && len(statuses) > 0 {
		info.State = statuses[0] // 取第一个状态，或者可以用 strings.Join(statuses, ",") 连接所有状态
	}

	// 线程数
	if numThreads, err := p.NumThreads(); err == nil {
		info.ThreadCount = int(numThreads)
	}

	// 文件描述符数量
	if numFDs, err := p.NumFDs(); err == nil {
		info.FileDescriptors = int(numFDs)
	}

	// 资源
	// 内存信息
	if memInfo, err := p.MemoryInfo(); err == nil {
		info.RSS = memInfo.RSS
		info.VMS = memInfo.VMS
	}

	// CPU时间
	if cpuTimes, err := p.Times(); err == nil {
		info.CPUUser = cpuTimes.User
		info.CPUSystem = cpuTimes.System
	}

	// 获取CPU使用率 - 优化版本
	// 方式1：使用非阻塞方式（可能返回0）
	cpuPercent, _ := p.CPUPercent()
	info.CPUPercent = cpuPercent

	// 方式2：使用带超时的上下文，避免长时间阻塞
	// ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	// defer cancel()
	// cpuPercent, err := p.CPUPercentWithContext(ctx)
	// if err != nil {
	// 	// 超时或其他错误时使用0
	// 	info.CPUPercent = 0
	// } else {
	// 	info.CPUPercent = cpuPercent
	// }

	// 磁盘
	// IO统计
	if ioCounters, err := p.IOCounters(); err == nil {
		info.IOReadCount = ioCounters.ReadCount
		info.IOWriteCount = ioCounters.WriteCount
		info.IOReadBytes = ioCounters.ReadBytes
		info.IOWriteBytes = ioCounters.WriteBytes
	}

	// 网络
	// 网络连接和端口
	if connections, err := p.Connections(); err == nil {
		info.NetConnections = len(connections)

		var tcpPorts, udpPorts []uint16
		for _, conn := range connections {
			if conn.Status == "LISTEN" && conn.Laddr.Port != 0 {
				switch conn.Type {
				case 1: // TCP
					tcpPorts = append(tcpPorts, uint16(conn.Laddr.Port))
				case 2: // UDP
					udpPorts = append(udpPorts, uint16(conn.Laddr.Port))
				}
			}
		}
		info.TCPPorts = tcpPorts
		info.UDPPorts = udpPorts
	}

	return info, nil
}

// GetProcessInfoWithSysinfo 使用go-sysinfo高效获取进程信息
func GetProcessInfoWithSysinfo() ([]ProcessInfo, error) {
	processes, err := sysinfo.Processes()
	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	var result []ProcessInfo
	for _, p := range processes {
		info, err := getProcessInfoDetailWithSysinfo(p)
		if err != nil {
			// 记录错误但继续处理其他进程
			continue
		}

		result = append(result, info)
	}

	// 打印结果统计
	fmt.Printf("获取到 %d 个进程信息:\n", len(result))

	// 打印前几个进程的详细信息
	// for i, proc := range result {
	// 	fmt.Printf("进程 %d:\n", i+1)
	// 	fmt.Printf("  PID=%d, PPID=%d, 名称=%s\n", proc.PID, proc.PPID, proc.Name)
	// 	fmt.Printf("  用户=%s(UID:%s), 状态=%s\n", proc.Username, proc.UID, proc.State)
	// 	fmt.Printf("  内存: RSS=%d bytes, VMS=%d bytes\n", proc.RSS, proc.VMS)
	// 	fmt.Printf("  CPU: 用户态=%.2fs, 系统态=%.2fs\n", proc.CPUUser, proc.CPUSystem)
	// 	fmt.Printf("  线程数=%d, 文件描述符=%d\n", proc.ThreadCount, proc.FileDescriptors)
	// 	fmt.Printf("  网络连接数=%d\n", proc.NetConnections)
	// 	if len(proc.TCPPorts) > 0 {
	// 		fmt.Printf("  TCP端口: %v\n", proc.TCPPorts)
	// 	}
	// 	if len(proc.UDPPorts) > 0 {
	// 		fmt.Printf("  UDP端口: %v\n", proc.UDPPorts)
	// 	}
	// 	fmt.Printf("  启动时间=%s\n", proc.StartTime)
	// 	fmt.Printf("  执行路径=%s\n", proc.ExePath)
	// 	fmt.Printf("  命令行=%s\n", proc.Cmdline)

	// 	fmt.Println("  ----------------------------------------")
	// }

	return result, nil
}

// getProcessInfoDetailWithSysinfo 获取单个进程的详细信息
func getProcessInfoDetailWithSysinfo(p types.Process) (ProcessInfo, error) {
	var info ProcessInfo

	// 获取进程基本信息
	processInfo, err := p.Info()
	if err != nil {
		return info, fmt.Errorf("获取进程基本信息失败: %v", err)
	}

	// 基本信息
	info.PID = processInfo.PID
	info.PPID = processInfo.PPID
	info.Name = processInfo.Name
	info.ExePath = processInfo.Exe
	info.Cmdline = fmt.Sprintf("%v", processInfo.Args)
	info.StartTime = processInfo.StartTime.Format("2006-01-02 15:04:05")

	// 用户信息
	if userInfo, err := p.User(); err == nil {
		info.UID = userInfo.UID
		if u, err := user.LookupId(userInfo.UID); err == nil {
			info.Username = u.Username
		}
	}

	// 内存信息
	if memInfo, err := p.Memory(); err == nil {
		info.RSS = memInfo.Resident
		info.VMS = memInfo.Virtual
	}

	// CPU时间和使用率计算
	if cpuInfo, err := p.CPUTime(); err == nil {
		info.CPUUser = cpuInfo.User.Seconds()
		info.CPUSystem = cpuInfo.System.Seconds()

		// 计算进程运行时长
		runningTime := time.Since(processInfo.StartTime).Seconds()
		if runningTime > 0 {
			// CPU使用率 = (用户态时间 + 系统态时间) / 运行时长 * 100
			totalCPUTime := info.CPUUser + info.CPUSystem
			info.CPUPercent = (totalCPUTime / runningTime) * 100
		}
	}

	return info, nil
}
