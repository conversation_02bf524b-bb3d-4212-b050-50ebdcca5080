package global

import "flag"

var (
	// 全局 http端口
	globalHttpPort int

	// 日志等级
	logLevelDefault string
	// 是否支持动态调整日志级别
	logLevelDynamic bool
	// 日志目录
	logDir string
	// 日志文件名
	logFileName string
	// 日志文件最大大小
	logMaxSize int
	// 日志文件最大年龄
	logMaxAge int
	// 日志文件最大备份数
	logMaxBackups int

	// prometheus http端口
	prometheusHttpPort int
	// 抓取间隔
	prometheusScrapeBufferSize int64
	// 缓存数据量
	prometheusScrapeInterval int64
	// 进程信息来源
	prometheusProcessSource string

	// uas2g http端口
	uas2gPort int
	// jwt过期时间
	jwtExpireDuration int64
	// jwt签发人
	jwtIssuer string
	// jwt密钥
	jwtKey string
)

func (s *DefaultSetting) Parse() {

	// 解析命令行参数，并保存到变量中
	flag.IntVar(&globalHttpPort, "global.http.port", 0, "全局HTTP服务端口, 默认0不启动")
	flag.StringVar(&logLevelDefault, "log.level.default", "info", "日志级别")
	flag.BoolVar(&logLevelDynamic, "log.level.dynamic", false, "是否支持动态调整日志级别")
	flag.StringVar(&logDir, "log.dir", "logs", "日志目录")
	flag.StringVar(&logFileName, "log.filename", "app.log", "日志文件名")
	flag.IntVar(&logMaxSize, "log.maxsize", 10, "日志文件最大大小")
	flag.IntVar(&logMaxAge, "log.maxage", 7, "日志文件最大年龄")
	flag.IntVar(&logMaxBackups, "log.maxbackups", 3, "日志文件最大备份数")
	// prometheus
	flag.IntVar(&prometheusHttpPort, "prometheus.http.port", 9109, "Prometheus HTTP服务端口")
	flag.Int64Var(&prometheusScrapeInterval, "prometheus.scrape.interval", 1, "抓取间隔, 单位秒")
	flag.Int64Var(&prometheusScrapeBufferSize, "prometheus.scrape.buffersize", 1, "缓存数据量")
	flag.StringVar(&prometheusProcessSource, "prometheus.process.source", "sysinfo", "进程信息来源, gopsutil/sysinfo")
	// uas2g
	flag.IntVar(&uas2gPort, "uas2g.port", 1010, "uas2g HTTP服务端口")
	// jwt
	flag.StringVar(&jwtIssuer, "jwt.issuer", "uas2g-system", "jwt签发人")
	flag.StringVar(&jwtKey, "jwt.key", "uas2g_secret_key", "jwt密钥")
	flag.Int64Var(&jwtExpireDuration, "jwt.expire.duration", 60, "jwt过期时间, 单位秒")

	// 解析命令行参数，并保存到变量中，flag.Parse() 必须在 flag.StringVar() 之后调用
	flag.Parse()

	// 保存配置到静态配置中，以便后续使用
	s.StaticConfig.Http.Port = globalHttpPort

	s.StaticConfig.ZapLogConfig.LogLevel = logLevelDefault
	s.StaticConfig.ZapLogConfig.IsDynamicLogLevel = logLevelDynamic
	s.StaticConfig.ZapLogConfig.LogDir = logDir
	s.StaticConfig.ZapLogConfig.LogFileName = logFileName
	s.StaticConfig.ZapLogConfig.LogMaxSize = logMaxSize
	s.StaticConfig.ZapLogConfig.LogMaxAge = logMaxAge
	s.StaticConfig.ZapLogConfig.LogMaxBackups = logMaxBackups

	s.StaticConfig.Prometheus.Port = prometheusHttpPort
	s.StaticConfig.Prometheus.ScrapeInterval = prometheusScrapeInterval
	s.StaticConfig.Prometheus.ScrapeBufferSize = prometheusScrapeBufferSize

	s.StaticConfig.UAS2G.Port = uas2gPort

	s.StaticConfig.JWTConfig.JwtExpireDuration = jwtExpireDuration
	s.StaticConfig.JWTConfig.JwtIssuer = jwtIssuer
	s.StaticConfig.JWTConfig.JwtKey = jwtKey

}
