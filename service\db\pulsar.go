package db

import (
	"context"
	"fmt"
	"log"

	"github.com/apache/pulsar-client-go/pulsar"
)

func Producer() {
	// 创建 Pulsar 客户端
	client, err := pulsar.NewClient(pulsar.ClientOptions{
		URL: "pulsar://localhost:6650", // Pulsar 服务地址
	})
	if err != nil {
		log.Fatalf("Could not instantiate Pulsar client: %v", err)
	}
	defer client.Close()

	// 创建生产者
	producer, err := client.CreateProducer(pulsar.ProducerOptions{
		Topic: "persistent://public/default/my-topic", // 主题名称
	})
	if err != nil {
		log.Fatalf("Could not create producer: %v", err)
	}
	defer producer.Close()

	// 发送消息
	for i := 0; i < 10; i++ {
		msg := fmt.Sprintf("message-%d", i)
		msgID, err := producer.Send(context.Background(), &pulsar.ProducerMessage{
			Payload: []byte(msg),
		})
		if err != nil {
			log.Fatalf("Failed to publish message: %v", err)
		}
		log.Printf("Published message: %v, ID: %v", msg, msgID)
	}
}

func Consumer() {
	// 创建 Pulsar 客户端
	client, err := pulsar.NewClient(pulsar.ClientOptions{
		URL: "pulsar://localhost:6650", // Pulsar 服务地址
	})
	if err != nil {
		log.Fatalf("Could not instantiate Pulsar client: %v", err)
	}
	defer client.Close()

	// 创建消费者
	consumer, err := client.Subscribe(pulsar.ConsumerOptions{
		Topic:            "persistent://public/default/my-topic", // 主题名称
		SubscriptionName: "my-subscription",                      // 订阅名称
		Type:             pulsar.Shared,                          // 订阅类型
	})
	if err != nil {
		log.Fatalf("Could not create consumer: %v", err)
	}
	defer consumer.Close()

	// 接收消息
	for i := 0; i < 10; i++ {
		msg, err := consumer.Receive(context.Background())
		if err != nil {
			log.Fatalf("Error receiving message: %v", err)
		}

		log.Printf("Received message: %s, ID: %v", string(msg.Payload()), msg.ID())

		// 确认消息
		consumer.Ack(msg)
	}
}
func ConsumerAsync() {
	client, err := pulsar.NewClient(pulsar.ClientOptions{
		URL: "pulsar://localhost:6650",
	})
	if err != nil {
		log.Fatal(err)
	}
	defer client.Close()

	consumer, err := client.Subscribe(pulsar.ConsumerOptions{
		Topic:            "persistent://public/default/my-topic",
		SubscriptionName: "my-subscription",
		Type:             pulsar.Shared,
	})
	if err != nil {
		log.Fatal(err)
	}
	defer consumer.Close()

	// 使用通道接收消息
	for msg := range consumer.Chan() {
		log.Printf("Received message: %s", string(msg.Payload()))
		consumer.Ack(msg)
	}
}
