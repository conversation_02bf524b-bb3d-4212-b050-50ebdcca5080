<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6bb5a323-f23a-4cc2-84b0-dbb88d95ca40" name="变更" comment="">
      <change afterPath="$PROJECT_DIR$/shrcb/sqlmonitor/db.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/go.mod" beforeDir="false" afterPath="$PROJECT_DIR$/go.mod" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/go.sum" beforeDir="false" afterPath="$PROJECT_DIR$/go.sum" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/main.go" beforeDir="false" afterPath="$PROJECT_DIR$/main.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shrcb/sqlmonitor/index.go" beforeDir="false" afterPath="$PROJECT_DIR$/shrcb/sqlmonitor/index.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shrcb/sqlmonitor/model.go" beforeDir="false" afterPath="$PROJECT_DIR$/shrcb/sqlmonitor/model.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/go/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="false" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="31AFXsMPzvekRa4KzeGhJFgbzw6" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DefaultGoTemplateProperty" value="Go File" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="go.format.on.save.advertiser.fired" value="true" />
    <property name="go.formatter.settings.were.checked" value="true" />
    <property name="go.import.settings.migrated" value="true" />
    <property name="go.modules.go.list.on.any.changes.was.set" value="true" />
    <property name="go.sdk.automatically.set" value="true" />
    <property name="go.watchers.conflict.with.on.save.actions.check.performed" value="true" />
    <property name="last_opened_file_path" value="$USER_HOME$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="rearrange.code.on.save" value="true" />
    <property name="settings.editor.selected.configurable" value="actions.on.save" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <integration-enabled>true</integration-enabled>
  </component>
</project>