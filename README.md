# victoriaMetricsCollector

## 项目概述

`victoriaMetricsCollector` 是一个基于 Go 语言开发的多功能数据收集和监控系统，主要用于收集各种系统指标并通过 Prometheus 格式暴露监控数据。

## 核心功能

### 🔍 监控与指标收集
- **Prometheus 指标收集器**：支持动态指标收集和暴露
- **系统进程监控**：获取详细的进程信息，包括 CPU、内存使用情况
- **自定义指标**：支持自定义 Prometheus 指标收集
- **VictoriaMetrics 集成**：高性能时序数据库支持

### 🌐 Web 服务
- **HTTP 服务**：提供 RESTful API 接口
- **Gin 框架**：高性能 HTTP Web 框架
- **健康检查**：内置健康检查端点
- **访问日志中间件**：记录 HTTP 请求访问日志
- **CORS 支持**：跨域资源共享支持

### 📁 文件传输
- **FTP 客户端**：支持 FTP/FTPS 文件上传下载
- **递归传输**：支持目录递归上传下载
- **进度显示**：文件传输进度条显示
- **配置管理**：支持保存和管理多个 FTP 服务器配置
- **TLS/SSL 支持**：支持 FTP over TLS/SSL 安全传输

### 🔧 数据库支持
- **MySQL**：MySQL 数据库连接和操作
- **SQLite**：轻量级数据库支持，支持 GORM
- **IBM DB2**：企业级数据库支持
- **Redis**：缓存数据库支持

### 🚀 其他功能
- **Web 自动化**：集成 Playwright 进行 Web 自动化测试
- **消息队列**：支持 Kafka 和 Pulsar 消息队列
- **Elasticsearch**：搜索引擎集成
- **JWT 认证**：支持 JWT 令牌认证
- **系统信息收集**：CPU、内存、磁盘等系统信息监控

## 技术栈

### 后端框架
- **Go 1.23.1**：主要开发语言
- **Gin v1.10.1**：HTTP Web 框架
- **Gorilla Mux v1.8.1**：HTTP 路由器和中间件

### 监控与指标
- **VictoriaMetrics v1.37.0**：高性能时序数据库
- **Prometheus Client v1.22.0**：指标收集和暴露

### 日志系统
- **Zap v1.27.0**：高性能结构化日志
- **Lumberjack v2.0.0**：日志轮转管理

### 数据库驱动
- **go-sql-driver/mysql v1.9.2**：MySQL 驱动
- **mattn/go-sqlite3 v1.14.28**：SQLite 驱动
- **ibmdb/go_ibm_db v0.5.2**：IBM DB2 驱动
- **redis/go-redis v9.11.0**：Redis 客户端
- **GORM v1.30.0**：Go ORM 框架

### 其他依赖
- **Playwright v0.3900.1**：Web 自动化测试
- **Kafka Go v0.4.48**：Kafka 客户端
- **Pulsar Client v0.15.1**：Apache Pulsar 客户端
- **Elasticsearch v8.18.0**：Elasticsearch 客户端
- **JWT v5.2.2**：JWT 令牌处理

## 项目结构

```
victoriaMetricsCollector/
├── main.go                    # 主程序入口
├── go.mod                     # Go 模块依赖
├── go.sum                     # 依赖校验文件
├── global/                    # 全局配置和设置
├── service/                   # 服务模块
│   ├── ftp/                  # FTP 服务
│   │   └── demo1/            # FTP 客户端示例
│   ├── web/                  # Web 服务
│   ├── prometheus/           # Prometheus 指标收集
│   │   ├── collector.go      # 自定义收集器
│   │   ├── exporter.go       # 指标导出器
│   │   └── middleware.go     # HTTP 中间件
│   ├── process/              # 进程监控
│   │   ├── psutil.go         # 进程工具
│   │   └── sysinfo.go        # 系统信息
│   ├── victoriaMetrics/      # VictoriaMetrics 集成
│   └── localLogs/            # 日志管理
├── demo/                     # 示例代码
└── logs/                     # 日志文件目录
```

## 配置参数

### 命令行参数
```bash
-httpPort=9108                 # 全局 HTTP 服务端口
-prometheusHttpPort=9109       # Prometheus HTTP 服务端口
-logLevel=info                 # 日志级别 (debug/info/warn/error)
-isDynamicLogLevel=false       # 是否支持动态调整日志级别
-logDir=logs                   # 日志目录
-logFileName=app.log           # 日志文件名
-logMaxSize=10                 # 日志文件最大大小(MB)
-logMaxAge=30                  # 日志文件保留天数
-logMaxBackups=5               # 日志文件备份数量
-scrapeInterval=15s            # 指标抓取间隔
-scrapeBufferSize=1000         # 缓存数据量
```

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd victoriaMetricsCollector
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 运行程序
```bash
# 使用默认配置运行
go run main.go

# 自定义参数运行
go run main.go -httpPort=8080 -prometheusHttpPort=9090 -logLevel=debug
```

### 4. 构建可执行文件
```bash
go build -o victoriaMetricsCollector main.go
./victoriaMetricsCollector
```

## API 端点

### 监控端点
- `GET /metrics` - Prometheus 指标端点
- `GET /health` - 健康检查端点
- `GET /ping` - 服务状态检查

### 示例请求
```bash
# 获取 Prometheus 指标
curl http://localhost:9109/metrics

# 健康检查
curl http://localhost:9108/health

# 服务状态检查
curl http://localhost:9108/ping
```

## FTP 客户端使用

### 保存服务器配置
```bash
./ftpClient --save --server=myserver --host=ftp.example.com --user=myuser --pass=mypass --tls
```

### 查看保存的配置
```bash
./ftpClient --show-configs
```

### 文件传输操作
```bash
# 上传文件/目录
./ftpClient --server=myserver --put --src=/local/path --dest=/remote/path --r

# 下载文件/目录
./ftpClient --server=myserver --get --src=/remote/path --dest=/local/path --r

# 列出远程目录内容
./ftpClient --server=myserver --list --src=/remote/path
```

### TLS/SSL 支持
```bash
# 启用TLS (默认验证证书)
./ftpClient --host=ftp.example.com --tls

# 启用TLS但不验证证书
./ftpClient --host=ftp.example.com --tls --insecure
```

## 开发指南

### 添加新的指标收集器
1. 在 `service/prometheus/` 目录下创建新的收集器文件
2. 实现 `prometheus.Collector` 接口
3. 在主程序中注册收集器

### 添加新的 Web 端点
1. 在 `service/web/` 目录下添加处理函数
2. 使用 Gin 框架定义路由
3. 添加必要的中间件

### 日志记录
```go
import "log/slog"

// 记录信息日志
slog.Info("操作成功", "key", "value")

// 记录错误日志
slog.Error("操作失败", "error", err.Error())
```

## 监控指标

### 系统指标
- CPU 使用率
- 内存使用情况
- 磁盘 I/O
- 网络流量

### 应用指标
- HTTP 请求计数
- 响应时间分布
- 错误率统计
- 自定义业务指标

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :9108
   
   # 使用不同端口启动
   go run main.go -httpPort=8080
   ```

2. **日志文件权限问题**
   ```bash
   # 确保日志目录有写权限
   chmod 755 logs/
   ```

3. **依赖下载失败**
   ```bash
   # 设置 Go 代理
   export GOPROXY=https://goproxy.cn,direct
   go mod tidy
   ```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue：[GitHub Issues](https://github.com/your-repo/issues)
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0
- 初始版本发布
- 基础监控功能
- FTP 客户端支持
- Web 服务框架

---

**注意**：本项目仍在积极开发中，API 可能会发生变化。建议在生产环境使用前进行充分测试。

