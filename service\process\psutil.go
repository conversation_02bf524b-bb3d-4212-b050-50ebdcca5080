package process

import (
	"fmt"
	"os/user"
	"reflect"
	"strconv"
	"strings"
	"time"
	"victoriaMetricsCollector/global"

	"github.com/shirou/gopsutil/v3/process"
)

func PsUtil() ([]map[string]string, error) {
	// 存储结果
	var result []map[string]string

	// 获取进程列表
	processes, err := process.Processes()

	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	for _, p := range processes {
		info, err := parseGopsutil(p)
		if err != nil {
			// 记录错误但继续处理其他进程
			continue
		}

		result = append(result, info)
	}

	// 打印结果统计
	global.Setting.Logger.Sugar().Infof("获取到系统中共有 %d 个进程", len(processes))

	return result, nil
}

func parseGopsutil(p *process.Process) (map[string]string, error) {
	res := make(map[string]string)

	// 基本信息
	res["pid"] = fmt.Sprintf("%d", p.Pid)

	// 父进程ID
	if ppid, err := p.Ppid(); err == nil {
		res["ppid"] = fmt.Sprintf("%d", ppid)
	}

	// 进程名称
	name, err := p.Name()
	if err == nil {
		res["name"] = name
	}

	// 命令行参数
	if cmdline, err := p.Cmdline(); err == nil {
		if name == "java" {
			sps := strings.Split(cmdline, " ")
			var line []string
			for _, s := range sps {
				if strings.Contains(s, "-") || strings.Contains(s, "_") || strings.Contains(s, "/") {
					continue
				}
				if strings.Contains(s, ".") {
					{
						line = append(line, s)
					}
				}
				if len(line) > 0 {
					res["cmdline"] = cmdline
				}
			}
		} else {
			res["cmdline"] = cmdline
		}
	}

	// 启动时间
	if createTime, err := p.CreateTime(); err == nil {
		res["start_time"] = time.Unix(createTime/1000, 0).Format("2006-01-02 15:04:05")
	}

	// 可执行文件路径
	if exe, err := p.Exe(); err == nil {
		res["exe"] = exe
	}

	// 用户信息
	if uids, err := p.Uids(); err == nil && len(uids) > 0 {
		res["uid"] = strconv.Itoa(int(uids[0]))
		if u, err := user.LookupId(res["uid"]); err == nil {
			res["username"] = u.Username
		}
	}

	// 进程状态
	if statuses, err := p.Status(); err == nil && len(statuses) > 0 {
		res["state"] = statuses[0] // 取第一个状态，或者可以用 strings.Join(statuses, ",") 连接所有状态
	}

	// 线程数
	if numThreads, err := p.NumThreads(); err == nil {
		res["thread_count"] = fmt.Sprintf("%d", numThreads)
	}

	// 文件描述符数量
	if numFDs, err := p.NumFDs(); err == nil {
		res["file_descriptors"] = fmt.Sprintf("%d", numFDs)
	}

	// 内存信息
	if memInfo, err := p.MemoryInfo(); err == nil {
		res["rss"] = fmt.Sprintf("%d", memInfo.RSS)
		res["vms"] = fmt.Sprintf("%d", memInfo.VMS)
	}

	// CPU时间
	if cpuTimes, err := p.Times(); err == nil {
		res["cpu_user"] = fmt.Sprintf("%f", cpuTimes.User)
		res["cpu_system"] = fmt.Sprintf("%f", cpuTimes.System)
		res["cpu_total"] = fmt.Sprintf("%f", cpuTimes.Total())
	}

	cpuPercent, _ := p.CPUPercent()
	res["cpu_percent"] = fmt.Sprintf("%f", cpuPercent)

	// IO统计
	if ioCounters, err := p.IOCounters(); err == nil {
		res["io_read_count"] = fmt.Sprintf("%d", ioCounters.ReadCount)
		res["io_write_count"] = fmt.Sprintf("%d", ioCounters.WriteCount)
		res["io_read_bytes"] = fmt.Sprintf("%d", ioCounters.ReadBytes)
		res["io_write_bytes"] = fmt.Sprintf("%d", ioCounters.WriteBytes)
	}

	// 网络连接和端口
	if connections, err := p.Connections(); err == nil {
		res["net_connections"] = fmt.Sprintf("%d", len(connections))

		var tcpPorts, udpPorts []string
		for _, conn := range connections {
			if conn.Status == "LISTEN" && conn.Laddr.Port != 0 {
				switch conn.Type {
				case 1: // TCP
					tcpPorts = append(tcpPorts, strconv.Itoa(int(conn.Laddr.Port)))
				case 2: // UDP
					udpPorts = append(udpPorts, strconv.Itoa(int(conn.Laddr.Port)))
				}
			}
		}
		res["tcp_ports"] = strings.Join(tcpPorts, ",")
		res["udp_ports"] = strings.Join(udpPorts, ",")
	}

	return res, nil
}

type PsUtils struct {
	ScrapePort bool // 是否抓取端口信息
}

func (ps *PsUtils) GetProcessInfoWithGopsutil() ([]ProcessInfo, error) {
	var result []ProcessInfo

	// 获取进程列表
	processes, err := process.Processes()

	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	for _, p := range processes {
		info, err := ps.getProcessInfoDetailWithGopsutil(p)
		if err != nil {
			// 记录错误但继续处理其他进程
			continue
		}

		result = append(result, info)
	}

	// 打印结果统计
	fmt.Printf("获取到 %d 个进程信息:\n", len(result))

	return result, nil
}

func (ps *PsUtils) GetProcessInfoWithGopsutilWithMap() ([]map[string]string, error) {
	// 获取进程列表
	processes, err := process.Processes()
	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	var result []map[string]string
	for _, p := range processes {
		info, err := ps.getProcessInfoDetailWithGopsutil(p)
		if err != nil {
			continue
		}

		// 转换为map[string]string格式
		processMap := ps.convertProcessInfoToMap(info)
		result = append(result, processMap)
		// fmt.Println(processMap)
	}

	// fmt.Printf("获取到 %d 个进程信息:\n", len(result))
	return result, nil
}

func (ps *PsUtils) getProcessInfoDetailWithGopsutil(p *process.Process) (ProcessInfo, error) {
	var info ProcessInfo

	// 基本信息
	info.PID = int(p.Pid)

	// 进程名称
	name, err := p.Name()
	if err == nil {
		info.Name = name
	}

	// 父进程ID
	if ppid, err := p.Ppid(); err == nil {
		info.PPID = int(ppid)
	}

	// 命令行参数
	if cmdline, err := p.Cmdline(); err == nil {

		if name == "java" {
			sps := strings.Split(cmdline, " ")
			var line []string
			for _, s := range sps {
				if strings.Contains(s, "-") || strings.Contains(s, "_") || strings.Contains(s, "/") {
					continue
				}

				if strings.Contains(s, ".") {
					{
						line = append(line, s)
					}
				}

				if len(line) > 0 {
					cmdline = strings.Join(line, "_")
				}
			}

		} else {
			info.Cmdline = cmdline
		}
	}

	// 启动时间
	if createTime, err := p.CreateTime(); err == nil {
		info.StartTime = time.Unix(createTime/1000, 0).Format("2006-01-02 15:04:05")
	}

	// 可执行文件路径
	if exe, err := p.Exe(); err == nil {
		info.ExePath = exe
	}

	// 用户信息
	if uids, err := p.Uids(); err == nil && len(uids) > 0 {
		info.UID = strconv.Itoa(int(uids[0]))
		if u, err := user.LookupId(info.UID); err == nil {
			info.Username = u.Username
		}
	}

	// 进程状态
	if statuses, err := p.Status(); err == nil && len(statuses) > 0 {
		info.State = statuses[0] // 取第一个状态，或者可以用 strings.Join(statuses, ",") 连接所有状态
	}

	// 线程数
	if numThreads, err := p.NumThreads(); err == nil {
		info.ThreadCount = int(numThreads)
	}

	// 文件描述符数量
	if numFDs, err := p.NumFDs(); err == nil {
		info.FileDescriptors = int(numFDs)
	}

	// 资源
	// 内存信息
	if memInfo, err := p.MemoryInfo(); err == nil {
		info.RSS = memInfo.RSS
		info.VMS = memInfo.VMS
	}

	// CPU时间
	if cpuTimes, err := p.Times(); err == nil {
		info.CPUUser = cpuTimes.User
		info.CPUSystem = cpuTimes.System
	}

	// 获取CPU使用率 - 精确计算版本
	// if cpuTimes, err := p.Times(); err == nil {
	// 	// 获取进程启动时间用于计算运行时长
	// 	if createTime, err := p.CreateTime(); err == nil {
	// 		runningTime := time.Since(time.Unix(createTime/1000, 0)).Seconds()
	// 		if runningTime > 0 {
	// 			// CPU使用率 = (用户态时间 + 系统态时间) / 运行时长 * 100
	// 			totalCPUTime := cpuTimes.User + cpuTimes.System
	// 			info.CPUPercent = (totalCPUTime / runningTime) * 100
	// 		}
	// 	}
	// } else {
	// 	// 备用方案：使用gopsutil的CPUPercent
	// 	cpuPercent, _ := p.CPUPercent()
	// 	info.CPUPercent = cpuPercent
	// }

	cpuPercent, _ := p.CPUPercent()
	info.CPUPercent = cpuPercent

	// if info.Name == "main.exe" {
	// 	fmt.Println("cpuPercent:", info.CPUPercent)
	// }

	// 方式2：使用带超时的上下文，避免长时间阻塞
	// ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	// defer cancel()
	// cpuPercent, err := p.CPUPercentWithContext(ctx)
	// if err != nil {
	// 	// 超时或其他错误时使用0
	// 	info.CPUPercent = 0
	// } else {
	// 	info.CPUPercent = cpuPercent
	// }

	// 磁盘
	// IO统计
	if ioCounters, err := p.IOCounters(); err == nil {
		info.IOReadCount = ioCounters.ReadCount
		info.IOWriteCount = ioCounters.WriteCount
		info.IOReadBytes = ioCounters.ReadBytes
		info.IOWriteBytes = ioCounters.WriteBytes
	}

	// 网络
	// 网络连接和端口

	if ps.ScrapePort {
		if connections, err := p.Connections(); err == nil {
			info.NetConnections = len(connections)

			var tcpPorts, udpPorts []uint16
			for _, conn := range connections {
				if conn.Status == "LISTEN" && conn.Laddr.Port != 0 {
					switch conn.Type {
					case 1: // TCP
						tcpPorts = append(tcpPorts, uint16(conn.Laddr.Port))
					case 2: // UDP
						udpPorts = append(udpPorts, uint16(conn.Laddr.Port))
					}
				}
			}
			info.TCPPorts = tcpPorts
			info.UDPPorts = udpPorts
		}
	}

	return info, nil
}

// convertProcessInfoToMap 通过反射和JSON标签将ProcessInfo转换为map[string]string
func (ps *PsUtils) convertProcessInfoToMap(info ProcessInfo) map[string]string {
	result := make(map[string]string)

	v := reflect.ValueOf(info)
	t := reflect.TypeOf(info)

	// 递归处理嵌入的结构体
	ps.processStruct(v, t, result)

	return result
}

// processStruct 递归处理结构体字段
func (ps *PsUtils) processStruct(v reflect.Value, t reflect.Type, result map[string]string) {
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		// 如果是嵌入的结构体，递归处理
		if fieldType.Anonymous && field.Kind() == reflect.Struct {
			ps.processStruct(field, fieldType.Type, result)
			continue
		}

		// 获取JSON标签
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" || jsonTag == "-" {
			continue
		}

		// 处理标签中的选项（如omitempty）
		tagName := jsonTag
		if commaIdx := strings.Index(jsonTag, ","); commaIdx != -1 {
			tagName = jsonTag[:commaIdx]
		}

		// 转换字段值为字符串
		result[tagName] = ps.convertFieldToString(field)
	}
}

// convertFieldToString 将字段值转换为字符串
func (ps *PsUtils) convertFieldToString(field reflect.Value) string {
	switch field.Kind() {
	case reflect.String:
		return field.String()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(field.Int(), 10)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.FormatUint(field.Uint(), 10)
	case reflect.Float32, reflect.Float64:
		return strconv.FormatFloat(field.Float(), 'f', 2, 64)
	case reflect.Slice:
		if field.Type().Elem().Kind() == reflect.Uint16 {
			// 处理 []uint16 类型的端口数组
			var ports []string
			for j := 0; j < field.Len(); j++ {
				ports = append(ports, strconv.FormatUint(field.Index(j).Uint(), 10))
			}
			return strings.Join(ports, ",")
		}
		return fmt.Sprintf("%v", field.Interface())
	default:
		return fmt.Sprintf("%v", field.Interface())
	}
}
