package kafka

import (
	"container/heap"
	"context"
	"fmt"

	"github.com/segmentio/kafka-go"
)

// OrderedMessage 定义一个消息结构，包含行号和原始消息
type OrderedMessage struct {
	LineNumber int
	Message    kafka.Message
}

// MessageHeap 实现堆接口用于排序
type MessageHeap []OrderedMessage

func (h *MessageHeap) Len() int {
	return len(*h)
}

func (h *MessageHeap) Less(i, j int) bool {
	return (*h)[i].LineNumber < (*h)[j].LineNumber
}

func (h *MessageHeap) Swap(i, j int) {
	(*h)[i], (*h)[j] = (*h)[j], (*h)[i]
}

func (h *MessageHeap) Push(x any) {
	*h = append(*h, x.(OrderedMessage))
}

func (h *MessageHeap) Pop() any {
	old := *h
	n := len(old)
	x := old[n-1]
	// 移除最后一个元素
	*h = old[0 : n-1]
	return x
}

// 消费者处理逻辑
func ConsumeOrderedMessages(reader *kafka.Reader, windowSize int) {
	h := &MessageHeap{}
	heap.Init(h)
	expectedLine := 1

	for {
		msg, err := reader.ReadMessage(context.Background())
		if err != nil {
			// 处理错误
			continue
		}

		// 解析行号（假设消息中有line_number字段）
		// lineNumber := extractLineNumber(msg.Value) // 实现你自己的解析逻辑
		lineNumber := 10 // 实现你自己的解析逻辑

		heap.Push(h, OrderedMessage{LineNumber: lineNumber, Message: msg})

		// 处理缓冲区中按顺序的消息
		for h.Len() > 0 && (*h)[0].LineNumber == expectedLine {
			orderedMsg := heap.Pop(h).(OrderedMessage)
			fmt.Println(orderedMsg.Message) // 处理消息
			expectedLine++
		}

		// 防止内存泄漏，设置窗口大小
		if h.Len() > windowSize {
			fmt.Printf("Warning: Message buffer exceeded window size (%d). Dropping oldest message.\n", windowSize)
			heap.Pop(h)
		}
	}
}
