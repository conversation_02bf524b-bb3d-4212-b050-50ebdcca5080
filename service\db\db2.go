package db

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/ibmdb/go_ibm_db"
)

// 确保你的 DB2 服务器已启动并可以接受远程连接
// 连接字符串中的参数需要根据你的实际环境进行修改
// DB2 对大小写敏感，表名和列名可能需要使用双引号括起来
// 在生产环境中，建议使用连接池管理数据库连接

func TestDB2() {
	// 连接字符串格式
	// HOSTNAME=host;DATABASE=dbname;PORT=port;UID=username;PWD=password
	connStr := "HOSTNAME=localhost;DATABASE=testdb;PORT=50000;UID=db2inst1;PWD=password"

	// 打开数据库连接
	db, err := sql.Open("go_ibm_db", connStr)
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	// 测试连接
	err = db.Ping()
	if err != nil {
		log.Fatal(err)
	}

	fmt.Println("成功连接到DB2数据库!")
}

func queryData(db *sql.DB) {
	rows, err := db.Query("SELECT id, name, age FROM users WHERE age > ?", 18)
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var name string
		var age int
		err = rows.Scan(&id, &name, &age)
		if err != nil {
			log.Fatal(err)
		}
		fmt.Printf("ID: %d, Name: %s, Age: %d\n", id, name, age)
	}

	if err = rows.Err(); err != nil {
		log.Fatal(err)
	}
}
func insertData(db *sql.DB) {
	stmt, err := db.Prepare("INSERT INTO users(name, age) VALUES(?, ?)")
	if err != nil {
		log.Fatal(err)
	}
	defer stmt.Close()

	res, err := stmt.Exec("张三", 25)
	if err != nil {
		log.Fatal(err)
	}

	lastId, err := res.LastInsertId()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("插入成功，ID: %d\n", lastId)
}
func updateData(db *sql.DB) {
	stmt, err := db.Prepare("UPDATE users SET age = ? WHERE id = ?")
	if err != nil {
		log.Fatal(err)
	}
	defer stmt.Close()

	res, err := stmt.Exec(30, 1)
	if err != nil {
		log.Fatal(err)
	}

	rowsAffected, err := res.RowsAffected()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("更新了 %d 行数据\n", rowsAffected)
}
func deleteData(db *sql.DB) {
	stmt, err := db.Prepare("DELETE FROM users WHERE id = ?")
	if err != nil {
		log.Fatal(err)
	}
	defer stmt.Close()

	res, err := stmt.Exec(1)
	if err != nil {
		log.Fatal(err)
	}

	rowsAffected, err := res.RowsAffected()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("删除了 %d 行数据\n", rowsAffected)
}
func transactionExample(db *sql.DB) {
	tx, err := db.Begin()
	if err != nil {
		log.Fatal(err)
	}

	defer func() {
		if err != nil {
			tx.Rollback()
			return
		}
		err = tx.Commit()
		if err != nil {
			log.Fatal(err)
		}
	}()

	_, err = tx.Exec("UPDATE account SET balance = balance - ? WHERE id = ?", 100, 1)
	if err != nil {
		return
	}

	_, err = tx.Exec("UPDATE account SET balance = balance + ? WHERE id = ?", 100, 2)
	if err != nil {
		return
	}

	fmt.Println("事务执行成功!")
}
