package db

import (
	"database/sql"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"strings"
	"testing"
	"time"
)

// 推荐选择：
// 小于1000条: 事务批量模式
// 1000-10万条: 批量VALUES模式
// 超过10万条: LOAD DATA模式
// 高并发写入: 异步批量写入器

// 1. 单条写入模式
// SingleInsert 单条插入 - 最基础的方式
// 优点: 实现简单，代码清晰
// 缺点: 每次插入都会建立一次连接，性能最差
// 适用场景: 数据量小，对性能要求不高的场景
func SingleInsert(db *sql.DB, users []User) error {
	for _, user := range users {
		// 每次执行都会与数据库建立一次连接
		_, err := db.Exec("INSERT INTO users (name, email) VALUES (?, ?)",
			user.Name, user.Email)
		if err != nil {
			return err
		}
	}
	return nil
}

// 2. 预编译语句模式
// PreparedInsert 预编译语句插入
// 优点: 减少SQL解析开销，防止SQL注入
// 缺点: 仍然是单条插入，网络往返次数多
// 适用场景: 中小数据量，需要防止SQL注入的场景
func PreparedInsert(db *sql.DB, users []User) error {
	// 预编译SQL语句，只解析一次
	stmt, err := db.Prepare("INSERT INTO users (name, email) VALUES (?, ?)")
	if err != nil {
		return err
	}
	defer stmt.Close() // 确保语句被关闭

	for _, user := range users {
		// 执行预编译语句，减少SQL解析开销
		_, err = stmt.Exec(user.Name, user.Email)
		if err != nil {
			return err
		}
	}
	return nil
}

// 3. 事务批量模式
// TransactionInsert 事务批量插入
// 优点: 减少提交次数，提高性能，支持回滚
// 缺点: 仍然是单条插入，网络往返次数多
// 适用场景: 需要保证数据一致性的批量插入
func TransactionInsert(db *sql.DB, users []User) error {
	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err := tx.Rollback(); err != nil {
			log.Printf("Failed to rollback transaction: %v", err)
		}
	}() // 发生错误时回滚事务

	// 在事务中预编译语句
	stmt, err := tx.Prepare("INSERT INTO users (name, email) VALUES (?, ?)")
	if err != nil {
		return err
	}
	defer stmt.Close()

	for _, user := range users {
		// 在事务中执行语句
		_, err = stmt.Exec(user.Name, user.Email)
		if err != nil {
			return err
		}
	}

	// 提交事务，只有一次提交操作
	return tx.Commit()
}

// 4. 批量VALUES模式
// BatchValuesInsert 批量VALUES插入
// 优点: 大幅减少网络往返次数，性能显著提升
// 缺点: 单条SQL语句过长可能超出MySQL限制
// 适用场景: 大批量数据插入，对性能要求高
func BatchValuesInsert(db *sql.DB, users []User, batchSize int) error {
	// 分批处理，避免单条SQL过长
	for i := 0; i < len(users); i += batchSize {
		end := i + batchSize
		if end > len(users) {
			end = len(users)
		}

		batch := users[i:end]
		valueStrings := make([]string, 0, len(batch))
		valueArgs := make([]interface{}, 0, len(batch)*2)

		// 构建批量插入的VALUES部分
		for _, user := range batch {
			valueStrings = append(valueStrings, "(?, ?)")
			valueArgs = append(valueArgs, user.Name, user.Email)
		}

		// 拼接完整SQL: INSERT INTO users VALUES (?,?),(?,?),...
		stmt := fmt.Sprintf("INSERT INTO users (name, email) VALUES %s",
			strings.Join(valueStrings, ","))

		// 一次执行插入多条数据
		_, err := db.Exec(stmt, valueArgs...)
		if err != nil {
			return err
		}
	}
	return nil
}

// 5. LOAD DATA模式
// LoadDataInsert LOAD DATA INFILE方式
// 优点: MySQL原生支持的最快导入方式，适合超大数据量
// 缺点: 需要文件IO，实现复杂，需要服务器支持
// 适用场景: 百万级以上数据导入
func LoadDataInsert(db *sql.DB, users []User) error {
	// 创建临时CSV文件
	file, err := os.CreateTemp("", "users_*.csv")
	if err != nil {
		return err
	}
	defer os.Remove(file.Name()) // 使用完删除临时文件
	defer file.Close()

	// 将数据写入CSV文件
	writer := csv.NewWriter(file)
	for _, user := range users {
		if err := writer.Write([]string{user.Name, user.Email}); err != nil {
			return err
		}
	}
	writer.Flush()

	// 使用LOAD DATA INFILE导入数据
	// 注意: 需要MySQL服务器有文件读取权限
	query := fmt.Sprintf(`
        LOAD DATA LOCAL INFILE '%s' 
        INTO TABLE users 
        FIELDS TERMINATED BY ',' 
        LINES TERMINATED BY '\n' 
        (name, email)`, file.Name())

	_, err = db.Exec(query)
	return err
}

// 6. 异步批量写入器
// AsyncBatchWriter 异步批量写入器
// 优点: 非阻塞写入，适合高并发场景，自动批处理
// 缺点: 实现复杂，有内存占用，需要管理生命周期
// 适用场景: 高并发写入，对写入延迟不敏感的场景
type AsyncBatchWriter struct {
	db        *sql.DB       // 数据库连接
	batchSize int           // 批量大小
	buffer    chan User     // 用户数据缓冲通道
	done      chan struct{} // 关闭信号
}

// NewAsyncBatchWriter 创建新的异步批量写入器
// batchSize: 每批处理的记录数
// bufferSize: 缓冲区大小，决定可以缓存多少条记录
func NewAsyncBatchWriter(db *sql.DB, batchSize int, bufferSize int) *AsyncBatchWriter {
	writer := &AsyncBatchWriter{
		db:        db,
		batchSize: batchSize,
		buffer:    make(chan User, bufferSize),
		done:      make(chan struct{}),
	}

	// 启动后台工作协程
	go writer.worker()
	return writer
}

// AddUser 添加用户到写入队列
// 非阻塞方法，如果缓冲区满了会阻塞
func (w *AsyncBatchWriter) AddUser(user User) {
	w.buffer <- user
}

// worker 后台工作协程，负责批量写入数据
func (w *AsyncBatchWriter) worker() {
	batch := make([]User, 0, w.batchSize)
	ticker := time.NewTicker(time.Second) // 定时刷新，确保数据及时写入
	defer ticker.Stop()

	for {
		select {
		case user := <-w.buffer:
			// 收到新数据，添加到批次
			batch = append(batch, user)
			if len(batch) >= w.batchSize {
				// 达到批次大小，立即刷新
				w.flushBatch(batch)
				batch = batch[:0] // 清空批次
			}
		case <-ticker.C:
			// 定时刷新，确保数据不会长时间滞留在内存
			if len(batch) > 0 {
				w.flushBatch(batch)
				batch = batch[:0]
			}
		case <-w.done:
			// 收到关闭信号，退出工作协程
			return
		}
	}
}

// flushBatch 将批次数据写入数据库
func (w *AsyncBatchWriter) flushBatch(batch []User) {
	if len(batch) == 0 {
		return
	}

	// 使用批量VALUES插入
	valueStrings := make([]string, 0, len(batch))
	valueArgs := make([]interface{}, 0, len(batch)*2)

	for _, user := range batch {
		valueStrings = append(valueStrings, "(?, ?)")
		valueArgs = append(valueArgs, user.Name, user.Email)
	}

	stmt := fmt.Sprintf("INSERT INTO users (name, email) VALUES %s",
		strings.Join(valueStrings, ","))

	_, err := w.db.Exec(stmt, valueArgs...)
	if err != nil {
		// 处理错误，可以加入重试逻辑
		fmt.Printf("Failed to flush batch: %v\n", err)
	}
}

// Close 关闭异步写入器，应在程序结束前调用
func (w *AsyncBatchWriter) Close() {
	close(w.done)
}

// 性能对比测试
// 运行方式: go test -bench=BenchmarkWriteModes -benchmem
func BenchmarkWriteModes(b *testing.B) {
	db := setupTestDB()
	users := generateTestUsers(10000)

	// 测试单条插入性能
	b.Run("SingleInsert", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			SingleInsert(db, users)
		}
	})

	// 测试预编译语句插入性能
	b.Run("PreparedInsert", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			PreparedInsert(db, users)
		}
	})

	// 测试事务批量插入性能
	b.Run("TransactionInsert", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			TransactionInsert(db, users)
		}
	})

	// 测试批量VALUES插入性能
	b.Run("BatchValuesInsert", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			BatchValuesInsert(db, users, 1000)
		}
	})
}

// setupTestDB 设置测试数据库连接
func setupTestDB() *sql.DB {
	// 测试数据库连接配置
	dsn := "root:password@tcp(localhost:3306)/testdb?parseTime=true"

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}

	// 测试连接
	if err = db.Ping(); err != nil {
		log.Fatalf("Failed to ping test database: %v", err)
	}

	// 创建测试表
	createTable(db)

	// 清空表数据
	db.Exec("TRUNCATE TABLE users")

	return db
}

// generateTestUsers 生成测试用户数据
func generateTestUsers(count int) []User {
	users := make([]User, count)

	for i := 0; i < count; i++ {
		users[i] = User{
			Name:  fmt.Sprintf("User_%d", i),
			Email: fmt.Sprintf("<EMAIL>", i),
		}
	}

	return users
}
