package prometheus

import (
	"log"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func Basic() {
	// 创建自定义指标
	customCounter := prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "myapp_custom_counter_total",
			Help: "This is my custom counter",
		},
	)

	customGauge := prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "myapp_custom_gauge",
			Help: "This is my custom gauge",
		},
	)

	customHistogram := prometheus.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "myapp_custom_histogram_seconds",
			Help:    "This is my custom histogram",
			Buckets: prometheus.LinearBuckets(0.1, 0.1, 10), // 从0.1开始，每个桶增加0.1，共10个桶
		},
	)

	customSummary := prometheus.NewSummary(
		prometheus.SummaryOpts{
			Name: "myapp_custom_summary_seconds",
			Help: "This is my custom summary",
			Objectives: map[float64]float64{
				0.5:  0.05,  // 50th percentile with 5% tolerance
				0.9:  0.01,  // 90th percentile with 1% tolerance
				0.99: 0.001, // 99th percentile with 0.1% tolerance
			},
		},
	)

	// 注册指标
	prometheus.MustRegister(customCounter)
	prometheus.MustRegister(customGauge)
	prometheus.MustRegister(customHistogram)
	prometheus.MustRegister(customSummary)

	// 启动goroutine定期更新指标
	go func() {
		for {
			// 更新计数器
			customCounter.Inc()

			// 更新仪表
			customGauge.Set(float64(time.Now().Unix() % 100))

			// 更新直方图
			customHistogram.Observe(float64(time.Now().UnixNano()%1000000000) / 1000000000.0)

			// 更新摘要
			customSummary.Observe(float64(time.Now().UnixNano()%500000000) / 1000000000.0)

			time.Sleep(2 * time.Second)
		}
	}()

	// 暴露指标端点
	http.Handle("/metrics", promhttp.Handler())
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func WithLabel() {
	// 创建带标签的计数器
	labeledCounter := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "myapp_http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "path", "status"},
	)

	// 创建带标签的直方图
	responseTimeHistogram := prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "myapp_http_response_time_seconds",
			Help:    "HTTP response time in seconds",
			Buckets: prometheus.ExponentialBuckets(0.001, 2, 16), // 从0.001开始，每个桶是前一个的2倍，共16个桶
		},
		[]string{"method", "path"},
	)

	// 注册指标
	prometheus.MustRegister(labeledCounter)
	prometheus.MustRegister(responseTimeHistogram)

	// 模拟HTTP请求处理
	go func() {
		paths := []string{"/", "/users", "/products"}
		methods := []string{"GET", "POST"}
		statusCodes := []string{"200", "404", "500"}

		for {
			// 随机选择一些值
			path := paths[time.Now().Unix()%int64(len(paths))]
			method := methods[time.Now().Unix()%int64(len(methods))]
			status := statusCodes[time.Now().Unix()%int64(len(statusCodes))]
			responseTime := float64(time.Now().UnixNano()%1000000000) / 1000000000.0

			// 记录指标
			labeledCounter.WithLabelValues(method, path, status).Inc()
			responseTimeHistogram.WithLabelValues(method, path).Observe(responseTime)

			time.Sleep(time.Second)
		}
	}()

	// 暴露指标端点
	http.Handle("/metrics", promhttp.Handler())
	log.Fatal(http.ListenAndServe(":9080", nil))
}
