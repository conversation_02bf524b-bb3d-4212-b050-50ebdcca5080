提示词
Go FTP 递归上传/下载，添加进度条，实现配置文件保存常用服务器配置，支持FTP over TLS/SSL，智能模式传输示例（自动识别文件类型），命令行 ftpClient --put/get --src=/opt --dest=/opt, --list。



# 保存服务器配置
./ftpClient --save --server=myserver --host=ftp.example.com --user=myuser --pass=mypass --tls

# 查看保存的配置
./ftpClient --show-configs

# 使用保存的配置
./ftpClient --server=myserver --list --src=/remote/path



# 上传文件/目录
./ftpClient --server=myserver --put --src=/local/path --dest=/remote/path [--r]

# 下载文件/目录
./ftpClient --server=myserver --get --src=/remote/path --dest=/local/path [--r]

# 列出远程目录内容
./ftpClient --server=myserver --list --src=/remote/path





# 启用TLS (默认验证证书)
./ftpClient --host=ftp.example.com --tls ...

# 启用TLS但不验证证书
./ftpClient --host=ftp.example.com --tls --insecure ...


功能特点
智能传输模式：

自动根据文件扩展名选择二进制或文本模式

支持多种常见文本文件格式

递归传输：

支持递归上传/下载整个目录

自动创建必要的目录结构

进度显示：

使用进度条显示传输进度

显示传输速度和剩余时间

可禁用进度显示

TLS/SSL 支持：

支持显式 FTP over TLS (FTPS)

可选择跳过证书验证

配置文件管理：

保存常用服务器配置

支持多个服务器配置

可设置默认服务器

文件列表功能：

显示远程目录内容

区分文件和目录

显示文件大小和修改时间

命令行友好：

清晰的参数说明

详细的错误提示

支持相对路径和绝对路径


