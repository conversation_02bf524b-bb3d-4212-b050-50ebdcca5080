package victoriaMetrics

import (
	"fmt"
	"log/slog"
	"runtime"
	"time"
)

func Demo() {
	// service.WriteToVMWithMetrics()
	// service.WriteToVMWClient()

	// mb := victoriaMetrics.NewMetricBuffer("http://11.11.11.11:8428", 1000, 5*time.Second)

	// mb.AddMetric("test", 1.1, map[string]string{"aa": "1", "bb": "2"}, 0)
	// for {
	// 	time.Sleep(time.Hour * 10)
	// }

	// 初始化客户端池
	pool := NewHttpClientPool(10, 30*time.Second)

	// 创建批量写入器
	writer := NewVMBatchWriter(pool, "http://11.11.11.11:8428/api/v1/import/prometheus", 100, 10)

	go func() {

		for {
			time.Sleep(time.Second * 1)

			slog.Info(fmt.Sprintf("NumGoroutine:%v", runtime.NumGoroutine()))
			slog.Info(fmt.Sprintf("WorkerCount:%v", writer.WorkerCount))
		}

	}()

	// 模拟写入数据
	for i := 0; i < 100000; i++ {

		metric := fmt.Sprintf("temperature{city=\"beijing\",room=\"living\"} %.2f %d", 23.5+float64(i%10)/10, time.Now().UnixNano()/1e9)

		// fmt.Println(metric)
		writer.WriteMetricToChan(metric)

		// 模拟生产环境中的间隔
		time.Sleep(time.Millisecond * 10)

		// slog.Info(fmt.Sprintf("NumGoroutine:%v", runtime.NumGoroutine()))
		// slog.Info(fmt.Sprintf("WorkerCount:%v", writer.WorkerCount))

	}

	// 最后确保所有数据都已刷新
	// if err := writer.Flush(); err != nil {
	// 	fmt.Printf("Final flush error: %v\n", err)
	// }

}
