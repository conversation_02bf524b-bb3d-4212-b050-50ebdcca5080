package sqlmonitor

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (s *SQLMonitor) StartMonitor() {
	engine := gin.Default()

	// 跨域中间件

	engine.Use(Cors())
	engine.POST("/api/v1/task/exec", s.CreateTaskExec)
	engine.Get("/api/v1/task/exec", s.CreateTaskExecDemo)
	engine.POST("/api/v1/task/exec/update", s.UpdateTaskExec)
	engine.POST("/api/v1/task/exec/select", s.QueryTaskExec)
	engine.POST("/api/v1/task/exec/delete/id", s.DeleteTaskExec)

	s.DB = GetOrmMySQL()

	_ = s.DB.AutoMigrate(&TaskExec{})

	_ = engine.Run(":1010")

}

func (s *SQLMonitor) CreateTaskExecDemo(ctx *gin.Context) {

	// 模拟数据量
	demoNum := 1000

	var demoDataList []TaskExec

	groups := []string{"系统维护", "数据备份", "监控告警", "日志清理", "性能优化"}
	frequencyValues := []string{"10", "20", "30", "40", "50"}

	var params TaskExec
	_ = ctx.ShouldBind(&params)

	fmt.Println(params)

	s.DB.Create(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100

	ctx.JSON(200, response)

}

func (s *SQLMonitor) CreateTaskExec(ctx *gin.Context) {

	var params TaskExec
	_ = ctx.ShouldBind(&params)

	fmt.Println(params)

	s.DB.Create(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100
	//response.Data = []TaskExec{}

	ctx.JSON(200, response)

}

func (s *SQLMonitor) UpdateTaskExec(ctx *gin.Context) {

	var params TaskExec
	_ = ctx.ShouldBind(&params)

	fmt.Println(params)

	//s.DB.Create(&params)
	//s.DB.Updates(&params)
	s.DB.Model(TaskExec{}).Where("id = ?", params.Id).Updates(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100
	//response.Data = []TaskExec{}

	ctx.JSON(200, response)

}

func (s *SQLMonitor) DeleteTaskExec(ctx *gin.Context) {

	// 前端请求数据
	type DeleteId struct {
		IdList []int `json:"ids"`
	}

	// 返回前端数据
	type DeleteStatus struct {
		Name   string `json:"name"`
		Status bool   `json:"status"`
	}

	var deleteStatusList []DeleteStatus

	var params DeleteId
	_ = ctx.ShouldBind(&params)

	for _, v := range params.IdList {
		var tmp TaskExec
		s.DB.Debug().First(&tmp, v)
		fmt.Println(tmp)

		var deleteStatus DeleteStatus

		deleteStatus.Name = tmp.Name

		res := s.DB.Delete(&TaskExec{}, v)

		if res.RowsAffected == 1 {
			deleteStatus.Status = true
		} else {
			deleteStatus.Status = false
		}
		deleteStatusList = append(deleteStatusList, deleteStatus)
	}

	//bty, _ := json.Marshal(&deleteStatusList)

	var response Response

	response.Success = true
	response.Message = "删除成功"
	response.Total = int64(len(deleteStatusList))
	response.Data = deleteStatusList

	ctx.JSON(200, response)

}

func (s *SQLMonitor) QueryTaskExec(ctx *gin.Context) {

	var params SearchParams
	_ = ctx.ShouldBind(&params)

	// 设置默认分页参数
	if params.Current <= 0 {
		params.Current = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 计算偏移量
	offset := (params.Current - 1) * params.PageSize

	var res []TaskExec

	s.DB.Debug().Limit(params.PageSize).Offset(offset).Find(&res)

	//fmt.Println(res)

	// 统计总记录数
	var total int64
	s.DB.Model(&TaskExec{}).Count(&total)

	var response Response

	//bty, _ := json.Marshal(&res)

	response.Success = true
	response.Message = "查询成功"
	response.Total = total
	response.Data = res

	ctx.JSON(200, response)

}

// Cors 处理跨域请求,支持options访问
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			// 允许所有来源
			c.Header("Access-Control-Allow-Origin", "*")
			// 允许的请求方法
			c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
			// 允许的请求头
			c.Header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
			// 允许暴露的响应头
			c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
			// 是否允许携带cookie
			c.Header("Access-Control-Allow-Credentials", "true")
		}

		// 放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}

		c.Next()
	}
}
