package global

import (
	"net/http"
	"victoriaMetricsCollector/service/localLogs"
)

// 初始化日志
func (s *DefaultSetting) InitZapLog() {
	logManager, err := localLogs.NewLoggerManager(s.StaticConfig.ZapLogConfig)
	if err != nil {
		panic(err)
	}

	// 初始化日志
	// s.Logger = localLogs.NewZapLog(config)
	s.Logger = logManager.GetLogger()

	// 如果配置支持动态日志级别，则设置API
	if s.StaticConfig.ZapLogConfig.IsDynamicLogLevel && s.ServeMux != nil {
		setupLogLevelAPI(s.ServeMux, logManager)
	}

}

// HTTP 接口控制日志级别
func setupLogLevelAPI(mux *http.ServeMux, logManager *localLogs.LoggerManager) {
	// 创建独立的路由器，避免与主服务冲突
	// mux := http.NewServeMux()

	// 注册日志级别控制接口
	mux.HandleFunc("/api/log/level", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case "POST":
			level := r.FormValue("level")
			if err := logManager.SetLevel(level); err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write([]byte("日志级别已更新为: " + level))
		case "GET":
			// 添加获取当前日志级别的功能
			w.Header().Set("Content-Type", "application/json")
			_, _ = w.Write([]byte(`{"current_level": "` + logManager.GetLogger().Level().String() + `"}`))
		}
	})

	// 添加健康检查接口
	mux.HandleFunc("/api/log/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("日志管理服务正常"))
	})

}
