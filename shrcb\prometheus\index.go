package prometheus

import (
	"log"
	"net/http"
	"strconv"
	"time"
	"victoriaMetricsCollector/global"
	"victoriaMetricsCollector/shrcb/restful"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func StartCollector(port int, scrapeInterval, scrapeBufferSize int64) {

	source := global.Setting.StaticConfig.Prometheus.ProcessSource

	var processCollector *DynamicCollector

	switch source {
	case "sysinfo":
		processCollector = NewCollectorWithSysInfo(scrapeInterval, scrapeBufferSize)
	case "gopsutil":
		processCollector = NewCollectorWithPsutil(scrapeInterval, scrapeBufferSize)
	default:
		processCollector = NewCollectorWithSysInfo(scrapeInterval, scrapeBufferSize)
	}

	prometheus.MustRegister(processCollector)

	// 添加健康状态指标
	healthStatus := prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "health_status",
			Help: "Health status of the service (1 = healthy, 0 = unhealthy)",
		},
	)

	// 注册健康状态指标
	prometheus.MustRegister(healthStatus)

	// 设置健康状态
	go func() {
		for {
			healthStatus.Set(1) // 初始状态为健康
			time.Sleep(10 * time.Second)
		}
	}()

	// HTTP
	router := restful.NewRouter()
	// 中间件
	router.Use(restful.LoggingMiddleware)

	// 设置HTTP路由
	router.HandleFunc("/", RootPage)
	// 健康检查端点
	router.HandleFunc("/health", HealthCheck)
	// 指标端点
	router.HandleFunc("/metrics", promhttp.Handler().ServeHTTP)

	// 启动HTTP服务器
	addr := ":" + strconv.Itoa(port)
	global.Setting.Logger.Sugar().Infof("Prometheus Starting server on %s", addr)
	log.Fatal(http.ListenAndServe(addr, router))

}
