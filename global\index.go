package global

import (
	"net/http"
	"victoriaMetricsCollector/service/localLogs"

	"go.uber.org/zap"
)

var Setting = &DefaultSetting{}

// 全局配置
type DefaultSetting struct {
	Logger       *zap.Logger    // 日志记录器
	ServeMux     *http.ServeMux // HTTP服务
	StaticConfig StaticConfig   // 静态配置
}

// 静态配置
type StaticConfig struct {
	Http         Http                   // 全局HTTP配置
	Prometheus   Prometheus             // Prometheus配置
	JWTConfig    JWTConfig              // JWT配置
	UAS2G        UAS2G                  // uas2g配置
	ZapLogConfig localLogs.ZapLogConfig // 日志配置
}

// HTTP配置
type Http struct {
	Port int
}

// Prometheus配置
type Prometheus struct {
	Port             int    // Prometheus HTTP服务端口
	ScrapeInterval   int64  // 抓取间隔
	ScrapeBufferSize int64  // 缓存数据量
	ProcessSource    string // 进程信息来源, gopsutil/sysinfo/both
}

type JWTConfig struct {
	JwtExpireDuration int64  // jwt过期时间, 单位秒
	JwtIssuer         string // jwt签发人
	JwtKey            string // jwt密钥
}

type UAS2G struct {
	Port int // uas2g HTTP服务端口
}
