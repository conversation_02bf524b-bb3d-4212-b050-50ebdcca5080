package db

import (
	"database/sql"
	"fmt"
	"strings"
	"testing"
	"time"
)

// 推荐选择：
// 少于100条: 事务批量更新
// 100-1万条: CASE WHEN批量更新
// 1万-10万条: ON DUPLICATE KEY UPDATE
// 超过10万条: 临时表批量更新
// 高并发场景: 异步批量更新器

// 1. 单条更新模式
// SingleUpdate 单条更新 - 最基础的方式
// 优点: 实现简单，代码清晰，精确控制
// 缺点: 每次更新都会建立一次连接，性能最差
// 适用场景: 少量数据更新，需要精确控制每条记录
func SingleUpdate(db *sql.DB, users []User) error {
	for _, user := range users {
		// 每次执行都会与数据库建立一次连接
		_, err := db.Exec("UPDATE users SET name = ?, email = ? WHERE id = ?",
			user.Name, user.Email, user.ID)
		if err != nil {
			return err
		}
	}
	return nil
}

// 2. 预编译语句更新模式
// PreparedUpdate 预编译语句更新
// 优点: 减少SQL解析开销，防止SQL注入，性能比单条更新好
// 缺点: 仍然是单条更新，网络往返次数多
// 适用场景: 中小数据量更新，需要防止SQL注入
func PreparedUpdate(db *sql.DB, users []User) error {
	// 预编译SQL语句，只解析一次
	stmt, err := db.Prepare("UPDATE users SET name = ?, email = ? WHERE id = ?")
	if err != nil {
		return err
	}
	defer stmt.Close() // 确保语句被关闭

	for _, user := range users {
		// 执行预编译语句，减少SQL解析开销
		_, err = stmt.Exec(user.Name, user.Email, user.ID)
		if err != nil {
			return err
		}
	}
	return nil
}

// 3. 事务批量更新模式
// TransactionUpdate 事务批量更新
// 优点: 减少提交次数，保证数据一致性，支持回滚
// 缺点: 仍然是单条更新，但在事务中执行
// 适用场景: 需要保证数据一致性的批量更新
func TransactionUpdate(db *sql.DB, users []User) error {
	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback() // 发生错误时回滚事务

	// 在事务中预编译语句
	stmt, err := tx.Prepare("UPDATE users SET name = ?, email = ? WHERE id = ?")
	if err != nil {
		return err
	}
	defer stmt.Close()

	for _, user := range users {
		// 在事务中执行更新语句
		_, err = stmt.Exec(user.Name, user.Email, user.ID)
		if err != nil {
			return err
		}
	}

	// 提交事务，只有一次提交操作
	return tx.Commit()
}

// 4. CASE WHEN批量更新模式
// CaseWhenUpdate CASE WHEN批量更新
// 优点: 单条SQL更新多行，网络往返次数少，性能优秀
// 缺点: SQL语句复杂，有长度限制，实现复杂
// 适用场景: 大批量数据更新，对性能要求高
func CaseWhenUpdate(db *sql.DB, users []User, batchSize int) error {
	// 分批处理，避免单条SQL过长
	for i := 0; i < len(users); i += batchSize {
		end := i + batchSize
		if end > len(users) {
			end = len(users)
		}

		batch := users[i:end]
		if len(batch) == 0 {
			continue
		}

		// 构建CASE WHEN语句
		nameCase := "CASE id"
		emailCase := "CASE id"
		ids := make([]string, 0, len(batch))
		args := make([]interface{}, 0, len(batch)*3)

		for _, user := range batch {
			nameCase += " WHEN ? THEN ?"
			emailCase += " WHEN ? THEN ?"
			ids = append(ids, fmt.Sprintf("%d", user.ID))

			// 参数顺序: id, name, id, email
			args = append(args, user.ID, user.Name, user.ID, user.Email)
		}

		nameCase += " ELSE name END"
		emailCase += " ELSE email END"

		// 构建完整的UPDATE语句
		sql := fmt.Sprintf(`
            UPDATE users 
            SET name = %s, email = %s 
            WHERE id IN (%s)`,
			nameCase, emailCase, strings.Join(ids, ","))

		// 执行批量更新
		_, err := db.Exec(sql, args...)
		if err != nil {
			return err
		}
	}
	return nil
}

// 5. ON DUPLICATE KEY UPDATE模式
// OnDuplicateKeyUpdate INSERT ... ON DUPLICATE KEY UPDATE
// 优点: 支持插入或更新，性能好，适合upsert场景
// 缺点: 需要唯一键约束，MySQL特有语法
// 适用场景: 不确定记录是否存在的批量upsert操作
func OnDuplicateKeyUpdate(db *sql.DB, users []User, batchSize int) error {
	// 分批处理
	for i := 0; i < len(users); i += batchSize {
		end := i + batchSize
		if end > len(users) {
			end = len(users)
		}

		batch := users[i:end]
		valueStrings := make([]string, 0, len(batch))
		valueArgs := make([]interface{}, 0, len(batch)*3)

		// 构建VALUES部分
		for _, user := range batch {
			valueStrings = append(valueStrings, "(?, ?, ?)")
			valueArgs = append(valueArgs, user.ID, user.Name, user.Email)
		}

		// 构建INSERT ... ON DUPLICATE KEY UPDATE语句
		sql := fmt.Sprintf(`
            INSERT INTO users (id, name, email) VALUES %s
            ON DUPLICATE KEY UPDATE 
            name = VALUES(name), 
            email = VALUES(email)`,
			strings.Join(valueStrings, ","))

		// 执行批量upsert
		_, err := db.Exec(sql, valueArgs...)
		if err != nil {
			return err
		}
	}
	return nil
}

// 6. 临时表批量更新模式
// TempTableUpdate 临时表批量更新
// 优点: 适合超大批量更新，性能优秀，支持复杂更新逻辑
// 缺点: 实现复杂，需要额外的表管理
// 适用场景: 超大批量数据更新，复杂的更新逻辑
func TempTableUpdate(db *sql.DB, users []User) error {
	// 创建临时表
	tempTableSQL := `
        CREATE TEMPORARY TABLE temp_users (
            id INT PRIMARY KEY,
            name VARCHAR(50),
            email VARCHAR(50)
        )`

	_, err := db.Exec(tempTableSQL)
	if err != nil {
		return err
	}
	defer db.Exec("DROP TEMPORARY TABLE IF EXISTS temp_users")

	// 批量插入数据到临时表
	err = BatchValuesInsert(db, users, 1000)
	if err != nil {
		return err
	}

	// 使用JOIN进行批量更新
	updateSQL := `
        UPDATE users u 
        INNER JOIN temp_users t ON u.id = t.id 
        SET u.name = t.name, u.email = t.email`

	_, err = db.Exec(updateSQL)
	return err
}

// 7. 异步批量更新器
// AsyncBatchUpdater 异步批量更新器
// 优点: 非阻塞更新，适合高并发场景，自动批处理
// 缺点: 实现复杂，有内存占用，需要管理生命周期
// 适用场景: 高并发更新，对更新延迟不敏感的场景
type AsyncBatchUpdater struct {
	db        *sql.DB       // 数据库连接
	batchSize int           // 批量大小
	buffer    chan User     // 用户数据缓冲通道
	done      chan struct{} // 关闭信号
}

// NewAsyncBatchUpdater 创建新的异步批量更新器
func NewAsyncBatchUpdater(db *sql.DB, batchSize int, bufferSize int) *AsyncBatchUpdater {
	updater := &AsyncBatchUpdater{
		db:        db,
		batchSize: batchSize,
		buffer:    make(chan User, bufferSize),
		done:      make(chan struct{}),
	}

	// 启动后台工作协程
	go updater.worker()
	return updater
}

// UpdateUser 添加用户到更新队列
func (u *AsyncBatchUpdater) UpdateUser(user User) {
	u.buffer <- user
}

// worker 后台工作协程，负责批量更新数据
func (u *AsyncBatchUpdater) worker() {
	batch := make([]User, 0, u.batchSize)
	ticker := time.NewTicker(time.Second) // 定时刷新
	defer ticker.Stop()

	for {
		select {
		case user := <-u.buffer:
			batch = append(batch, user)
			if len(batch) >= u.batchSize {
				u.flushBatch(batch)
				batch = batch[:0]
			}
		case <-ticker.C:
			if len(batch) > 0 {
				u.flushBatch(batch)
				batch = batch[:0]
			}
		case <-u.done:
			return
		}
	}
}

// flushBatch 批量刷新更新数据
func (u *AsyncBatchUpdater) flushBatch(batch []User) {
	if len(batch) == 0 {
		return
	}

	// 使用CASE WHEN方式批量更新
	err := CaseWhenUpdate(u.db, batch, len(batch))
	if err != nil {
		fmt.Printf("Failed to flush update batch: %v\n", err)
	}
}

// Close 关闭异步更新器
func (u *AsyncBatchUpdater) Close() {
	close(u.done)
}

// 性能对比测试
func BenchmarkUpdateModes(b *testing.B) {
	db := setupTestDB()
	users := generateTestUsersWithID(1000)

	// 预先插入测试数据
	BatchValuesInsert(db, users, 1000)

	b.Run("SingleUpdate", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			SingleUpdate(db, users)
		}
	})

	b.Run("PreparedUpdate", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			PreparedUpdate(db, users)
		}
	})

	b.Run("TransactionUpdate", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			TransactionUpdate(db, users)
		}
	})

	b.Run("CaseWhenUpdate", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			CaseWhenUpdate(db, users, 100)
		}
	})

	b.Run("OnDuplicateKeyUpdate", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			OnDuplicateKeyUpdate(db, users, 100)
		}
	})
}

// generateTestUsersWithID 生成带ID的测试用户数据
func generateTestUsersWithID(count int) []User {
	users := make([]User, count)

	for i := 0; i < count; i++ {
		users[i] = User{
			ID:    i + 1,
			Name:  fmt.Sprintf("UpdatedUser_%d", i),
			Email: fmt.Sprintf("<EMAIL>", i),
		}
	}

	return users
}
