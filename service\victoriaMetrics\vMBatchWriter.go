package victoriaMetrics

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync/atomic"
	"time"
)

type VMBatchWriter struct {
	pool          *HttpClientPool
	vmURL         string
	batchSize     int // 每批消息大小
	batchInterval int // 每批消息时长（秒）
	metricChan    chan string
	WorkerCount   int64
}

func NewVMBatchWriter(pool *HttpClientPool, vmURL string, batchSize int, workerNum int) *VMBatchWriter {
	writer := VMBatchWriter{
		pool:       pool,
		vmURL:      vmURL,
		batchSize:  batchSize,
		metricChan: make(chan string, 1000),
	}

	// 开启处理消息
	go writer.FlushWithWorkerN(int64(workerNum))

	return &writer
}

// 传递metric指标到指标管道metricChan
func (w *VMBatchWriter) WriteMetricToChan(metric string) {

	// 需要阻塞场景
	w.metricChan <- metric

}

// 每N个指标开启协程传递
func (w *VMBatchWriter) FlushWithWorkerN(workerNum int64) {

	for {
		// 每隔N时间段检查任务是否达到上限
		if w.WorkerCount < workerNum && len(w.metricChan) > 0 {

			// fmt.Println(w.WorkerCount, workerNum)
			buffer := make([]byte, 0, w.batchSize*1024)

			batchCount := 0
			batchStartTime := time.Now()
			for {
				select {
				case metric := <-w.metricChan:
					buffer = append(buffer, metric...)
					buffer = append(buffer, '\n')
					batchCount += 1
				default:
					// continue
					time.Sleep(time.Millisecond * 10)
				}

				// 数量达到阈值时退出
				if batchCount == w.batchSize {
					break
				}

				currentTime := time.Now()
				diff := currentTime.Sub(batchStartTime)
				// 时间阈值达到时退出
				if int(diff.Seconds()) > w.batchInterval {
					break
				}

			}

			go w.Worker(buffer)

		}

		time.Sleep(time.Microsecond * 500)

	}

}

func (w *VMBatchWriter) Worker(data []byte) {
	// 任务+1
	atomic.AddInt64(&w.WorkerCount, 1)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	err := w.SendMetrics(ctx, data)
	cancel()

	if err != nil {
		// 处理错误，可以加入重试逻辑或错误队列
		fmt.Printf("Failed to send batch: %v\n", err)
	}

	// time.Sleep(time.Second * 100)

	// 任务-1
	atomic.AddInt64(&w.WorkerCount, -1)

}

// 简单发送消息到VM, 多个字符串转字节时加个换行符\n
func (w *VMBatchWriter) SendMetrics(ctx context.Context, data []byte) error {
	client := w.pool.Get()

	u, err := url.Parse(w.vmURL)
	if err != nil {
		return fmt.Errorf("invalid VM URL: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", u.String(), bytes.NewReader(data))
	if err != nil {
		return fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Content-Type", "text/plain")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected status code %d: %s", resp.StatusCode, string(body))
	}

	return nil
}
