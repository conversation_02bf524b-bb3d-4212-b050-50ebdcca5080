package uas2g

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

var AuthHeader = "Authorization_UAS2G"

// JWT认证中间件
func JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Authorization头获取token
		authHeader := c.<PERSON>(AuthHeader)
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": fmt.Sprintf("缺少%s头", AuthHeader),
			})
			c.Abort()
			return
		}

		// 验证token
		claims, err := ValidateToken(authHeader)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "token验证失败",
				"error":   err.<PERSON>rror(),
			})
			c.Abort()
			return
		}

		// 将claims存储到上下文中
		c.Set("claims", claims)
		c.Next()
	}
}
