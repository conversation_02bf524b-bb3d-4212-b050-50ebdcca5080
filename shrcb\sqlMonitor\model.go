package sqlmonitor

import (
	"database/sql"
	"gorm.io/gorm"
	"time"
)

// SQLMonitor 主结构体
type SQLMonitor struct {
	DB *gorm.DB
}

// Response 返回数据
type Response struct {
	Data    any    `json:"data"`
	Total   int64  `json:"total"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// SearchParams 查询参数
type SearchParams struct {
	Current  int `json:"current"`
	PageSize int `json:"pageSize"`
}

// TaskExec 数据库模型
type TaskExec struct {
	Id             uint         `json:"id" gorm:"primarykey"`     // 主键
	Name           string       `json:"name"`                     // 任务名称
	Group          string       `json:"group"`                    // 任务分组
	Status         string       `json:"status"`                   // 任务状态
	StartTime      string       `json:"start_time"`               // 开始时间
	EndTime        string       `json:"end_time"`                 // 结束时间
	Weekday        string       `json:"weekday"`                  // 执行星期
	Frequency      string       `json:"frequency"`                // 批次执行频率
	RetryNum       string       `json:"retry_num"`                // 重试次数
	RetryFrequency string       `json:"retry_frequency"`          // 重试间隔
	AlertTaskId    string       `json:"alert_task_id"`            // 告警任务ID, 多个ID逗号分割
	AlertSendId    string       `json:"alert_send_id"`            // 告警发送方式ID, 多个ID逗号分割
	DBConnectionId string       `json:"db_connection_id"`         // db 连接id
	OtherInfoId    string       `json:"other_info_id"`            // 附加信息id
	CreatedAt      time.Time    `json:"create_time"`              // 创建日期
	UpdatedAt      time.Time    `json:"update_time"`              // 更新日期
	DeletedAt      sql.NullTime `json:"delete_time" gorm:"index"` // 删除日期
}

// TableName 表名
func (TaskExec) TableName() string {
	return "task_exec"
}
