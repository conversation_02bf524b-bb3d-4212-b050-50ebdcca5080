package global

import (
	"fmt"
	"log"
	"net/http"
)

func (s *DefaultSetting) Init() {
	// 解析命令行参数，并保存到静态配置中，以便后续使用
	s.Parse()

	// 是否开启HTTP监听
	if s.StaticConfig.Http.Port != 0 {
		// 配置一个全局HTTP服务, 用于动态配置某些服务
		// 如 通过这个HTTP接口动态调整日志级别
		s.ServeMux = http.NewServeMux()
	}

	// 初始化日志系统，并保存到静态配置中，以便后续使用
	s.InitZapLog()

	// 是否开启HTTP监听
	if s.StaticConfig.Http.Port != 0 && s.ServeMux != nil {
		s.InitHttpServer()
	}

}

// 初始化HTTP服务
func (s *DefaultSetting) InitHttpServer() {
	// 获取端口
	port := s.StaticConfig.Http.Port

	// 启动独立的HTTP服务器（使用不同端口避免冲突）
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port), // 使用独立端口
		Handler: s.ServeMux,
	}

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Panicf("全局HTTP服务启动失败: %v", err)
			// return err
		}
	}()

	s.Logger.Sugar().Infof("全局HTTP服务已启动, 监听端口: %d", port)

}
