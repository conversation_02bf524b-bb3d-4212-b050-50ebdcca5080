package db

import (
	"encoding/json"
	"log"
	"strings"

	"github.com/elastic/go-elasticsearch/v8"
)

func main() {
	// 配置客户端
	cfg := elasticsearch.Config{
		Addresses: []string{
			"http://localhost:9200",
		},
		// 如果需要认证
		// Username: "username",
		// Password: "password",
	}

	// 创建客户端
	es, err := elasticsearch.NewClient(cfg)
	if err != nil {
		log.Fatalf("Error creating the client: %s", err)
	}

	// 检查连接
	res, err := es.Info()
	if err != nil {
		log.Fatalf("Error getting response: %s", err)
	}
	defer res.Body.Close()

	log.Println(res)
}
func Conn() {
	// 配置客户端
	cfg := elasticsearch.Config{
		Addresses: []string{
			"http://localhost:9200",
		},
		// 如果需要认证
		// Username: "username",
		// Password: "password",
	}

	// 创建客户端
	es, err := elasticsearch.NewClient(cfg)
	if err != nil {
		log.Fatalf("Error creating the client: %s", err)
	}

	// 检查连接
	res, err := es.Info()
	if err != nil {
		log.Fatalf("Error getting response: %s", err)
	}
	defer res.Body.Close()

	log.Println(res)
}

func indexDocument(es *elasticsearch.Client) {
	// 文档数据
	doc := `{
		"title": "Go Elasticsearch Tutorial",
		"content": "This is a tutorial about using Elasticsearch with Go",
		"tags": ["golang", "elasticsearch", "tutorial"],
		"published": true,
		"views": 100
	}`

	// 索引文档 (index: "articles", id: "1")
	res, err := es.Index(
		"articles",                   // Index name
		strings.NewReader(doc),       // Document body
		es.Index.WithDocumentID("1"), // Document ID
		es.Index.WithRefresh("true"), // Refresh
	)
	if err != nil {
		log.Fatalf("Error indexing document: %s", err)
	}
	defer res.Body.Close()

	log.Println(res.String())
}

func searchDocuments(es *elasticsearch.Client) {
	// 构建查询
	query := `{
		"query": {
			"match": {
				"title": "tutorial"
			}
		}
	}`

	// 执行搜索
	res, err := es.Search(
		es.Search.WithIndex("articles"),
		es.Search.WithBody(strings.NewReader(query)),
		es.Search.WithPretty(),
	)
	if err != nil {
		log.Fatalf("Error searching documents: %s", err)
	}
	defer res.Body.Close()

	log.Println(res.String())
}

func updateDocument(es *elasticsearch.Client) {
	// 更新脚本
	script := `{
		"doc": {
			"views": 150
		}
	}`

	// 更新文档 (index: "articles", id: "1")
	res, err := es.Update(
		"articles",                // Index name
		"1",                       // Document ID
		strings.NewReader(script), // Update script
	)
	if err != nil {
		log.Fatalf("Error updating document: %s", err)
	}
	defer res.Body.Close()

	log.Println(res.String())
}

func deleteDocument(es *elasticsearch.Client) {
	// 删除文档 (index: "articles", id: "1")
	res, err := es.Delete(
		"articles", // Index name
		"1",        // Document ID
	)
	if err != nil {
		log.Fatalf("Error deleting document: %s", err)
	}
	defer res.Body.Close()

	log.Println(res.String())
}

func bulkOperations(es *elasticsearch.Client) {
	// 批量操作请求体
	var body strings.Builder
	body.WriteString(`{"index":{"_index":"articles","_id":"2"}}` + "\n")
	body.WriteString(`{"title":"Bulk Operation","content":"This is a bulk operation example","views":50}` + "\n")
	body.WriteString(`{"update":{"_index":"articles","_id":"1"}}` + "\n")
	body.WriteString(`{"doc":{"views":200}}` + "\n")

	res, err := es.Bulk(
		strings.NewReader(body.String()),
		es.Bulk.WithRefresh("true"),
	)
	if err != nil {
		log.Fatalf("Error performing bulk operations: %s", err)
	}
	defer res.Body.Close()

	log.Println(res.String())
}

func searchWithStructResponse(es *elasticsearch.Client) {
	query := `{
		"query": {
			"match_all": {}
		},
		"size": 1
	}`

	res, err := es.Search(
		es.Search.WithIndex("articles"),
		es.Search.WithBody(strings.NewReader(query)),
	)
	if err != nil {
		log.Fatalf("Error searching documents: %s", err)
	}
	defer res.Body.Close()

	// 解析响应
	var r map[string]interface{}
	if err := json.NewDecoder(res.Body).Decode(&r); err != nil {
		log.Fatalf("Error parsing the response body: %s", err)
	}

	// 打印命中结果
	for _, hit := range r["hits"].(map[string]interface{})["hits"].([]interface{}) {
		log.Printf("Document ID: %s", hit.(map[string]interface{})["_id"])
		log.Printf("Document source: %v", hit.(map[string]interface{})["_source"])
	}
}
