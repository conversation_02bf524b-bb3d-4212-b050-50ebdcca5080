package demo1

import (
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jlaffaye/ftp"
	"github.com/schollz/progressbar/v3"
)

const configFile = "ftpclient.json"

type ServerConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	TLS      bool   `json:"tls"`
}

type AppConfig struct {
	Servers       map[string]ServerConfig `json:"servers"`
	DefaultServer string                  `json:"default_server"`
}

func Demo1() {
	// 定义命令行参数
	putCmd := flag.Bool("put", false, "Upload file/directory to FTP server")
	getCmd := flag.Bool("get", false, "Download file/directory from FTP server")
	listCmd := flag.Bool("list", false, "List remote directory contents")
	srcPath := flag.String("src", "", "Source path (local or remote)")
	destPath := flag.String("dest", "", "Destination path (remote or local)")
	serverName := flag.String("server", "", "Server configuration name")
	host := flag.String("host", "localhost", "FTP server host")
	port := flag.String("port", "21", "FTP server port")
	username := flag.String("user", "anonymous", "FTP username")
	password := flag.String("pass", "anonymous", "FTP password")
	recursive := flag.Bool("r", false, "Recursive transfer for directories")
	showProgress := flag.Bool("progress", true, "Show progress bar")
	useTLS := flag.Bool("tls", false, "Use FTP over TLS/SSL")
	insecure := flag.Bool("insecure", false, "Skip TLS certificate verification")
	saveConfigFlag := flag.Bool("save", false, "Save current connection as configuration")
	showConfigs := flag.Bool("show-configs", false, "Show saved configurations")

	flag.Parse()

	// 加载配置文件
	config, err := loadConfig()
	if err != nil {
		log.Printf("警告: 无法加载配置文件: %v", err)
		config = &AppConfig{Servers: make(map[string]ServerConfig)}
	}

	// 显示保存的配置
	if *showConfigs {
		showSavedConfigs(config)
		return
	}

	// 验证参数
	if !*putCmd && !*getCmd && !*listCmd && !*saveConfigFlag {
		log.Fatal("必须指定操作类型 (--put, --get, --list 或 --save)")
	}

	// 获取服务器配置
	var serverConfig ServerConfig
	if *serverName != "" {
		var ok bool
		serverConfig, ok = config.Servers[*serverName]
		if !ok {
			log.Fatalf("未找到服务器配置: %s", *serverName)
		}
	} else {
		serverConfig = ServerConfig{
			Host:     *host,
			Port:     *port,
			Username: *username,
			Password: *password,
			TLS:      *useTLS,
		}
	}

	// 保存配置
	if *saveConfigFlag {
		if *serverName == "" {
			log.Fatal("必须指定 --server 名称来保存配置")
		}
		config.Servers[*serverName] = serverConfig
		if err := saveConfig(config); err != nil {
			log.Fatalf("保存配置失败: %v", err)
		}
		fmt.Printf("成功保存配置: %s\n", *serverName)
		return
	}

	// 连接 FTP 服务器
	client, err := ConnectFTP(serverConfig, *insecure)
	if err != nil {
		log.Fatalf("FTP连接失败: %v", err)
	}
	if err := client.Quit(); err != nil {
		log.Printf("Warning: Error closing FTP connection: %v", err)
	}

	fmt.Printf("成功连接到FTP服务器 %s:%s\n", serverConfig.Host, serverConfig.Port)

	// 执行操作
	switch {
	case *listCmd:
		listPath := *srcPath
		if listPath == "" {
			listPath = "."
		}
		err = ListDirectory(client, listPath)
	case *putCmd:
		if *srcPath == "" {
			log.Fatal("必须指定 --src 参数")
		}
		if *destPath == "" {
			log.Fatal("必须指定 --dest 参数")
		}
		if *recursive && isDir(*srcPath) {
			err = UploadDirectory(client, *srcPath, *destPath, *showProgress)
		} else {
			err = SmartUpload(client, *srcPath, *destPath, *showProgress)
		}
	case *getCmd:
		if *srcPath == "" {
			log.Fatal("必须指定 --src 参数")
		}
		if *destPath == "" {
			log.Fatal("必须指定 --dest 参数")
		}
		if *recursive {
			err = DownloadDirectory(client, *srcPath, *destPath, *showProgress)
		} else {
			err = SmartDownload(client, *srcPath, *destPath, *showProgress)
		}
	}

	if err != nil {
		log.Fatalf("操作失败: %v", err)
	}
}

func loadConfig() (*AppConfig, error) {
	file, err := os.ReadFile(configFile)
	if err != nil {
		return nil, err
	}

	var config AppConfig
	err = json.Unmarshal(file, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func saveConfig(config *AppConfig) error {
	file, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(configFile, file, 0644)
}

func showSavedConfigs(config *AppConfig) {
	if len(config.Servers) == 0 {
		fmt.Println("没有保存的服务器配置")
		return
	}

	fmt.Println("保存的服务器配置:")
	for name, server := range config.Servers {
		fmt.Printf("  %s:\n", name)
		fmt.Printf("    主机: %s\n", server.Host)
		fmt.Printf("    端口: %s\n", server.Port)
		fmt.Printf("    用户: %s\n", server.Username)
		fmt.Printf("    TLS: %v\n", server.TLS)
		fmt.Println()
	}
}

func ConnectFTP(config ServerConfig, insecure bool) (*ftp.ServerConn, error) {
	addr := fmt.Sprintf("%s:%s", config.Host, config.Port)

	var options []ftp.DialOption
	options = append(options, ftp.DialWithTimeout(10*time.Second))

	if config.TLS {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: insecure,
			ServerName:         config.Host,
		}
		options = append(options, ftp.DialWithTLS(tlsConfig))
	}

	c, err := ftp.Dial(addr, options...)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	err = c.Login(config.Username, config.Password)
	if err != nil {
		return nil, fmt.Errorf("登录失败: %v", err)
	}

	return c, nil
}

func IsBinaryFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	textExtensions := map[string]bool{
		".txt": true, ".csv": true, ".html": true, ".htm": true, ".xml": true,
		".json": true, ".log": true, ".md": true, ".go": true, ".c": true,
		".cpp": true, ".h": true, ".hpp": true, ".java": true, ".py": true,
		".sh": true, ".bat": true, ".php": true, ".js": true, ".css": true,
		".conf": true, ".ini": true, ".yml": true, ".yaml": true,
	}

	_, isText := textExtensions[ext]
	return !isText
}

func isDir(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

func SmartUpload(c *ftp.ServerConn, localPath, remotePath string, showProgress bool) error {
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("无法打开本地文件: %v", err)
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}

	isBinary := IsBinaryFile(localPath)
	if isBinary {
		err = c.Type(ftp.TransferTypeBinary)
	} else {
		err = c.Type(ftp.TransferTypeASCII)
	}
	if err != nil {
		return fmt.Errorf("设置传输模式失败: %v", err)
	}

	remoteDir := filepath.Dir(remotePath)
	if remoteDir != "" && remoteDir != "." {
		err = ensureRemoteDir(c, remoteDir)
		if err != nil {
			return err
		}
	}

	var reader io.Reader = file
	var bar *progressbar.ProgressBar

	if showProgress {
		bar = progressbar.NewOptions64(
			fileInfo.Size(),
			progressbar.OptionSetDescription(fmt.Sprintf("上传 %s", filepath.Base(localPath))),
			progressbar.OptionShowBytes(true),
			progressbar.OptionSetWidth(30),
			progressbar.OptionShowCount(),
			progressbar.OptionOnCompletion(func() {
				fmt.Println()
			}),
		)
		reader = io.TeeReader(file, bar)
	}

	err = c.Stor(filepath.Base(remotePath), reader)
	if err != nil {
		return fmt.Errorf("文件上传失败: %v", err)
	}

	if showProgress && bar != nil {
		if err := bar.Finish(); err != nil {
			log.Printf("Warning: Error finishing progress bar: %v", err)
		}
	}

	fmt.Printf("成功上传: %s -> %s [模式: %s]\n",
		localPath, remotePath, map[bool]string{true: "二进制", false: "文本"}[isBinary])
	return nil
}

func UploadDirectory(c *ftp.ServerConn, localDir, remoteDir string, showProgress bool) error {
	return filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		relPath, err := filepath.Rel(localDir, path)
		if err != nil {
			return err
		}

		remotePath := filepath.Join(remoteDir, relPath)
		return SmartUpload(c, path, remotePath, showProgress)
	})
}

func SmartDownload(c *ftp.ServerConn, remotePath, localPath string, showProgress bool) error {
	isBinary := IsBinaryFile(remotePath)
	if isBinary {
		err := c.Type(ftp.TransferTypeBinary)
		if err != nil {
			return fmt.Errorf("设置二进制模式失败: %v", err)
		}
	} else {
		err := c.Type(ftp.TransferTypeASCII)
		if err != nil {
			return fmt.Errorf("设置ASCII模式失败: %v", err)
		}
	}

	size, err := c.FileSize(remotePath)
	if err != nil {
		size = 0 // 如果无法获取大小，进度条将显示不确定状态
	}

	r, err := c.Retr(remotePath)
	if err != nil {
		return fmt.Errorf("获取远程文件失败: %v", err)
	}
	defer r.Close()

	localDir := filepath.Dir(localPath)
	if localDir != "" {
		err = os.MkdirAll(localDir, 0755)
		if err != nil {
			return fmt.Errorf("创建本地目录失败: %v", err)
		}
	}

	file, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %v", err)
	}
	defer file.Close()

	var writer io.Writer = file
	var bar *progressbar.ProgressBar

	if showProgress {
		bar = progressbar.NewOptions64(
			size,
			progressbar.OptionSetDescription(fmt.Sprintf("下载 %s", filepath.Base(remotePath))),
			progressbar.OptionShowBytes(true),
			progressbar.OptionSetWidth(30),
			progressbar.OptionShowCount(),
			progressbar.OptionOnCompletion(func() {
				fmt.Println()
			}),
		)
		writer = io.MultiWriter(file, bar)
	}

	_, err = io.Copy(writer, r)
	if err != nil {
		return fmt.Errorf("写入本地文件失败: %v", err)
	}

	if showProgress && bar != nil {
		if err := bar.Finish(); err != nil {
			log.Printf("Warning: Error finishing progress bar: %v", err)
		}
	}

	fmt.Printf("成功下载: %s -> %s [模式: %s]\n",
		remotePath, localPath, map[bool]string{true: "二进制", false: "文本"}[isBinary])
	return nil
}

func DownloadDirectory(c *ftp.ServerConn, remoteDir, localDir string, showProgress bool) error {
	walker := c.Walk(remoteDir)
	for walker.Next() {
		if walker.Stat().Type == ftp.EntryTypeFolder {
			continue
		}

		relPath, err := filepath.Rel(remoteDir, walker.Path())
		if err != nil {
			return err
		}

		localPath := filepath.Join(localDir, relPath)
		err = SmartDownload(c, walker.Path(), localPath, showProgress)
		if err != nil {
			return err
		}
	}
	return walker.Err()
}

func ListDirectory(c *ftp.ServerConn, path string) error {
	entries, err := c.List(path)
	if err != nil {
		return fmt.Errorf("列出目录失败: %v", err)
	}

	fmt.Printf("%s 目录内容:\n", path)
	for _, entry := range entries {
		switch entry.Type {
		case ftp.EntryTypeFolder:
			fmt.Printf("[DIR]  %-50s %s\n", entry.Name, entry.Time.Format("2006-01-02 15:04"))
		default:
			fmt.Printf("[FILE] %-50s %10d bytes %s\n",
				entry.Name, entry.Size, entry.Time.Format("2006-01-02 15:04"))
		}
	}
	return nil
}

func ensureRemoteDir(c *ftp.ServerConn, path string) error {
	dirs := strings.Split(path, "/")
	currentPath := ""

	for _, dir := range dirs {
		if dir == "" {
			continue
		}
		currentPath += "/" + dir

		err := c.ChangeDir(currentPath)
		if err != nil {
			err = c.MakeDir(currentPath)
			if err != nil {
				return fmt.Errorf("创建目录 %s 失败: %v", currentPath, err)
			}
			err = c.ChangeDir(currentPath)
			if err != nil {
				return fmt.Errorf("切换目录 %s 失败: %v", currentPath, err)
			}
		}
	}
	return nil
}
