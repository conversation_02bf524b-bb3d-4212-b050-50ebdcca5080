package uas2g

import (
	"fmt"
	"strings"
	"time"
	"victoriaMetricsCollector/global"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// 签发人，可以从环境变量或配置文件中获取，或者使用硬编码的字符串。在生产环境中，建议使用更安全的签发人设置。
// var Issuer = "uas2g-system"
var Issuer = global.Setting.StaticConfig.JWTConfig.JwtIssuer

// 用于签名的密钥字符串，可以从环境变量或配置文件中获取，或者使用硬编码的密钥字符串。在生产环境中，建议使用更安全的密钥生成方式。
// var JwtStr = "uas2g_secret_key"
var JwtStr = global.Setting.StaticConfig.JWTConfig.JwtKey

// 用于签名的密钥
var JWTKey = []byte(JwtStr)

// 自定义声明结构体
type Claims struct {
	jwt.RegisteredClaims
}

// 签发JWT令牌（直接返回Bearer格式）
func GenerateToken(expireDuration time.Duration) (string, error) {
	now := time.Now()

	// 创建声明
	claims := &Claims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(expireDuration)),
			ID:        uuid.New().String(), // 使用UUID作为JWTID
		},
	}

	// 使用HMAC256签名方法创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名并获取完整的编码后的字符串token
	tokenString, err := token.SignedString(JWTKey)
	if err != nil {
		return "", fmt.Errorf("生成token失败: %v", err)
	}

	return "Bearer " + tokenString, nil
}

// 验证JWT令牌（处理Bearer前缀）
func ValidateToken(bearerToken string) (*Claims, error) {
	// 检查Bearer前缀
	if len(bearerToken) < 7 || bearerToken[:7] != "Bearer " {
		return nil, fmt.Errorf("token格式错误, 需要Bearer前缀")
	}

	// 提取实际的token
	tokenString := strings.TrimSpace(bearerToken[7:])

	claims := &Claims{}

	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (any, error) {
		// 确保token使用的是HMAC256签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return JWTKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析token失败: %v", err)
	}

	// 检查token是否有效
	if !token.Valid {
		return nil, fmt.Errorf("无效的token")
	}

	return claims, nil
}

// 刷新令牌（生成新的Bearer token）
func RefreshToken(bearerToken string, newExpireDuration time.Duration) (string, error) {
	// 先验证旧token
	_, err := ValidateToken(bearerToken)
	if err != nil {
		return "", fmt.Errorf("旧token验证失败: %v", err)
	}

	// 生成新的Bearer token
	return GenerateToken(newExpireDuration)
}
