package auth

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// 用于签名的密钥
var JWTKey = []byte("my_secret_key")

// 自定义context key类型
type contextKey string

const claimsKey contextKey = "claims"

// 自定义声明结构体
type Claims struct {
	Username string `json:"username"`
	jwt.RegisteredClaims
}

func Test() {
	http.HandleFunc("/login", LoginHandler)
	http.HandleFunc("/protected", AuthMiddleware(ProtectedHandler))

	fmt.Println("Server started on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

// 登录处理，生成JWT
func LoginHandler(w http.ResponseWriter, r *http.Request) {
	// 这里应该验证用户名和密码，这里简化为直接生成token
	username := "exampleUser"

	// 设置token过期时间
	expirationTime := time.Now().Add(5 * time.Minute)

	// 创建声明
	claims := &Claims{
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			Issuer:    "test",
		},
	}

	// 生成token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(JWTKey)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// 设置cookie（可选）
	http.SetCookie(w, &http.Cookie{
		Name:    "token",
		Value:   tokenString,
		Expires: expirationTime,
	})

	// 返回token
	if _, err := w.Write([]byte(tokenString)); err != nil {
		log.Printf("Error writing response: %v", err)
	}
}

// 受保护的路由处理
func ProtectedHandler(w http.ResponseWriter, r *http.Request) {
	// 从上下文中获取claims
	claims, ok := r.Context().Value(claimsKey).(*Claims)
	if !ok {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	if _, err := w.Write([]byte(fmt.Sprintf("欢迎 %s，这是一个受保护的路由", claims.Username))); err != nil {
		log.Printf("Error writing response: %v", err)
	}
}

// 认证中间件
func AuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从cookie获取token（或者从Authorization头）
		c, err := r.Cookie("token")
		if err != nil {
			if err == http.ErrNoCookie {
				// 如果没有cookie，检查Authorization头
				authHeader := r.Header.Get("Authorization")
				if authHeader == "" {
					w.WriteHeader(http.StatusUnauthorized)
					return
				}

				// 格式应该是 "Bearer <token>"
				if len(authHeader) < 7 || authHeader[:7] != "Bearer " {
					w.WriteHeader(http.StatusUnauthorized)
					return
				}

				tokenString := authHeader[7:]
				claims, err := validateToken(tokenString)
				if err != nil {
					w.WriteHeader(http.StatusUnauthorized)
					return
				}

				// 将claims存入上下文
				ctx := context.WithValue(r.Context(), claimsKey, claims)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		tokenString := c.Value
		claims, err := validateToken(tokenString)
		if err != nil {
			w.WriteHeader(http.StatusUnauthorized)
			return
		}

		// 将claims存入上下文
		ctx := context.WithValue(r.Context(), claimsKey, claims)
		next.ServeHTTP(w, r.WithContext(ctx))
	}
}

// 验证token
func validateToken(tokenString string) (*Claims, error) {
	claims := &Claims{}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return JWTKey, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}
