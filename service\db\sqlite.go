package db

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"

	_ "modernc.org/sqlite"
)

func WithoutCGO() {
	// 打开数据库连接
	db, err := sql.Open("sqlite", "file:example.db?cache=shared&mode=memory")
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	// 创建表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			email TEXT UNIQUE NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		log.Fatal(err)
	}

	// 插入数据
	res, err := db.Exec("INSERT INTO users (name, email) VALUES (?, ?)", "Alice", "<EMAIL>")
	if err != nil {
		log.Fatal(err)
	}
	id, _ := res.LastInsertId()
	fmt.Printf("Inserted user with ID: %d\n", id)

	// 查询数据
	rows, err := db.Query("SELECT id, name, email, created_at FROM users")
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var name, email, createdAt string
		err = rows.Scan(&id, &name, &email, &createdAt)
		if err != nil {
			log.Fatal(err)
		}
		fmt.Printf("ID: %d, Name: %s, Email: %s, Created: %s\n", id, name, email, createdAt)
	}
}

func WithCGO() {
	// 打开数据库连接
	db, err := sql.Open("sqlite3", "./example.db")
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	// 创建表
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS products (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			price REAL,
			stock INTEGER DEFAULT 0,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		log.Fatal(err)
	}

	// 插入数据
	res, err := db.Exec("INSERT INTO products (name, price, stock) VALUES (?, ?, ?)",
		"Laptop", 999.99, 10)
	if err != nil {
		log.Fatal(err)
	}
	id, _ := res.LastInsertId()
	fmt.Printf("Inserted product with ID: %d\n", id)

	// 查询数据
	rows, err := db.Query("SELECT id, name, price, stock, updated_at FROM products")
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var name string
		var price float64
		var stock int
		var updatedAt string
		err = rows.Scan(&id, &name, &price, &stock, &updatedAt)
		if err != nil {
			log.Fatal(err)
		}
		fmt.Printf("ID: %d, Name: %s, Price: %.2f, Stock: %d, Updated: %s\n",
			id, name, price, stock, updatedAt)
	}

	// 事务示例
	tx, err := db.Begin()
	if err != nil {
		log.Fatal(err)
	}

	_, err = tx.Exec("UPDATE products SET stock = stock - 1 WHERE id = ?", id)
	if err != nil {
		tx.Rollback()
		log.Fatal(err)
	}

	_, err = tx.Exec("INSERT INTO products (name, price, stock) VALUES (?, ?, ?)",
		"Mouse", 29.99, 50)
	if err != nil {
		tx.Rollback()
		log.Fatal(err)
	}

	err = tx.Commit()
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("Transaction completed successfully")
}
