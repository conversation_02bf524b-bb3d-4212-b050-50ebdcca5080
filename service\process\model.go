package process

type ProcessInfo struct {
	// Name            string   // 进程名称
	// PID             int      // 进程ID
	// PPID            int      // 父进程ID
	// Cmdline         string   // 命令行参数
	// StartTime       string   // 启动时间
	// ExePath         string   // 可执行文件路径
	// UID             string   // 用户ID
	// Username        string   // 用户名
	// State           string // 进程状态
	// ThreadCount     int    // 线程数
	// FileDescriptors int    // 文件描述符数量
	ProcessBasic
	// RSS             uint64   // 常驻内存(RSS)
	// VMS             uint64   // 虚拟内存(VMS)
	// CPUUser         float64  // 用户态CPU时间
	// CPUSystem       float64  // 系统态CPU时间
	ProcessResource
	// IOReadCount    uint64   // IO读取次数
	// IOWriteCount   uint64   // IO写入次数
	// IOReadBytes    uint64   // IO读取字节数
	// IOWriteBytes   uint64   // IO写入字节数
	ProcessDisk
	// NetConnections int      // 网络连接数
	// TCPPorts       []uint16 // TCP监听端口
	// UDPPorts       []uint16 // UDP监听端口
	ProcessNet
}

type ProcessBasic struct {
	Name            string `json:"name"`             // 进程名称
	PID             int    `json:"pid"`              // 进程ID
	PPID            int    `json:"ppid"`             // 父进程ID
	Cmdline         string `json:"cmdline"`          // 命令行参数
	StartTime       string `json:"start_time"`       // 启动时间
	ExePath         string `json:"exe"`              // 可执行文件路径
	UID             string `json:"uid"`              // 用户ID
	Username        string `json:"username"`         // 用户名
	State           string `json:"state"`            // 进程状态
	ThreadCount     int    `json:"thread_count"`     // 线程数
	FileDescriptors int    `json:"file_descriptors"` // 文件描述符数量
}

type ProcessResource struct {
	RSS        uint64  `json:"rss"`         // 常驻内存(RSS)
	VMS        uint64  `json:"vms"`         // 虚拟内存(VMS)
	CPUUser    float64 `json:"cpu_user"`    // 用户态CPU时间
	CPUSystem  float64 `json:"cpu_system"`  // 系统态CPU时间
	CPUPercent float64 `json:"cpu_percent"` // CPU使用率
}

type ProcessDisk struct {
	IOReadCount  uint64 `json:"io_read_count"`  // IO读取次数
	IOWriteCount uint64 `json:"io_write_count"` // IO写入次数
	IOReadBytes  uint64 `json:"io_read_bytes"`  // IO读取字节数
	IOWriteBytes uint64 `json:"io_write_bytes"` // IO写入字节数
}

type ProcessNet struct {
	NetConnections int      `json:"net_connections"` // 网络连接数
	TCPPorts       []uint16 `json:"tcp_ports"`       // TCP监听端口
	UDPPorts       []uint16 `json:"udp_ports"`       // UDP监听端口
}
