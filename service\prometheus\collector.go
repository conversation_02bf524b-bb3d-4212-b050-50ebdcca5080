package prometheus

import (
	"log"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// CustomCollector 实现 prometheus.Collector 接口
type CustomCollector struct {
	customMetric *prometheus.Desc
}

// NewCustomCollector 创建新的收集器
func NewCustomCollector() *CustomCollector {
	return &CustomCollector{
		customMetric: prometheus.NewDesc(
			"myapp_custom_metric",
			"This is a custom metric from our application",
			[]string{"label1", "label2"},
			prometheus.Labels{"const_label": "some_value"},
		),
	}
}

// Describe 实现 prometheus.Collector 接口
func (c *CustomCollector) Describe(ch chan<- *prometheus.Desc) {
	ch <- c.customMetric
}

// Collect 实现 prometheus.Collector 接口
func (c *CustomCollector) Collect(ch chan<- prometheus.Metric) {
	// 这里可以是从数据库、API或其他地方获取的真实数据
	value := float64(time.Now().Unix() % 100)

	// 发送指标
	ch <- prometheus.MustNewConstMetric(
		c.customMetric,
		prometheus.GaugeValue,
		value,
		"value1", "value2", // 对应 label1 和 label2
	)
}

func CollectorTest() {
	// 创建并注册自定义收集器
	customCollector := NewCustomCollector()
	prometheus.MustRegister(customCollector)

	// 暴露指标端点
	http.Handle("/metrics", promhttp.Handler())
	log.Fatal(http.ListenAndServe(":8080", nil))
}
