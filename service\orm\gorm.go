package orm

import (
	"fmt"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func Test() {
	// 连接 SQLite 数据库
	db, err := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// 自动迁移（创建表）
	db.AutoMigrate(&User{})

	// 其他操作...

	// 创建记录
	user := User{Name: "John", Email: "<EMAIL>", Age: 30}
	result := db.Create(&user)
	if result.Error != nil {
		// 处理错误
	}
	fmt.Printf("Created user with ID: %d\n", user.ID)

	// 查询单个记录
	var foundUser User
	db.First(&foundUser, "email = ?", "<EMAIL>")
	fmt.Printf("Found user: %+v\n", foundUser)

	// 更新记录
	db.Model(&foundUser).Update("Age", 31)

	// 删除记录
	db.Delete(&foundUser)

	// 获取第一条记录（按主键排序）
	var firstUser User
	db.First(&firstUser)

	// 获取最后一条记录
	var lastUser User
	db.Last(&lastUser)

	// 条件查询
	var users []User
	db.Where("age > ?", 25).Find(&users)

	// 链式查询
	db.Where("name LIKE ?", "%jo%").Where("age >= ?", 25).Find(&users)

	// 选择特定字段
	db.Select("name", "age").Find(&users)

	// 排序
	db.Order("age desc").Find(&users)

	// 分页
	db.Limit(10).Offset(0).Find(&users) // 第一页，每页10条

	// 执行原生 SQL
	// var users []User
	db.Raw("SELECT * FROM users WHERE age > ?", 25).Scan(&users)

	// 执行不返回结果的 SQL
	db.Exec("UPDATE users SET name = ? WHERE id = ?", "New Name", 1)
}

// 定义模型
type User struct {
	gorm.Model
	Name  string
	Email string `gorm:"uniqueIndex"`
	Age   int
}
