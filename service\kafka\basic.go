package kafka

import (
	"context"
	"log/slog"
	"time"

	"github.com/segmentio/kafka-go"
)

type KafkaGo struct {
	Consumer Consumer
	Producer Producer
}

type Consumer struct {
	Host           []string
	IsStop         bool
	IsLatestOffset bool
}

type Producer struct {
	Host       []string
	WriteRetry int
}

// ReadFromKafkaToChan 从Kafka读取消息到通道
func (k *Consumer) ReadFromKafkaToChan(topic string, groupId string, ch chan kafka.Message) {
	// 配置读取器
	config := kafka.ReaderConfig{
		Brokers:        k.Host,
		GroupID:        groupId,
		Topic:          topic,
		MinBytes:       10e3,
		MaxBytes:       10e6,
		CommitInterval: time.Second,
	}

	// 创建读取器
	reader := kafka.NewReader(config)

	// 关闭读取器
	defer func(r *kafka.Reader) {
		err := r.Close()
		if err != nil {
			slog.Error("failed to close reader", "error", err.Error())
		}
	}(reader)

	// 如果需要读取最新的偏移量，则设置偏移量
	if k.IsLatestOffset {
		if err := reader.SetOffset(kafka.LastOffset); err != nil {
			slog.Error("failed to set offset", "error", err.Error())
		}
	}

	// 循环读取消息
	for {
		// 读取消息
		msg, err := reader.ReadMessage(context.Background())

		// 如果读取消息失败，则继续读取
		if err != nil {
			slog.Error("failed to read message", "error", err.Error())
			continue
		}

		// 将消息写入通道
		ch <- msg

		// 如果停止读取，则退出
		if k.IsStop {
			break
		}
	}
}

// WriteToKafka 向Kafka写入消息
func (k *Producer) WriteToKafka(topic string, key string, msg string) {
	// 创建写入器
	writer := &kafka.Writer{
		Addr:         kafka.TCP(k.Host...),
		Topic:        topic,
		Balancer:     &kafka.Hash{},
		RequiredAcks: kafka.RequireAll,
		WriteTimeout: 10 * time.Second,
		ReadTimeout:  10 * time.Second,
	}

	// 关闭写入器
	defer func(w *kafka.Writer) {
		err := w.Close()
		if err != nil {
			slog.Error("failed to close writer", "error", err.Error())
		}
	}(writer)

	// 写入消息
	err := writer.WriteMessages(
		context.Background(),
		kafka.Message{
			Key:   []byte(key),
			Value: []byte(msg),
		},
	)

	// 重写最大次数
	k.WriteRetry = 3

	// 重写次数
	count := 0

	if err != nil {
		if count < k.WriteRetry {
			slog.Error("failed to write message", "error", err.Error(), "write count", count)
			time.Sleep(1 * time.Second)
			k.WriteToKafka(topic, key, msg)
		} else {
			slog.Error("write message to kafka failed", "error", err.Error(), "write count", count)
		}

		// 重写次数+1
		// count++

	}

	slog.Info("write message to kafka success", "topic", topic, "key", key, "msg", msg)

}
