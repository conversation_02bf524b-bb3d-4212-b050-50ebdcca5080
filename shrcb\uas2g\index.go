package uas2g

type Account struct {
	ActionType  string  `json:"action_type" `
	RequestID   string  `json:"request_id" `
	AccountNo   string  `json:"account_no" `
	AccountName string  `json:"account_name" `
	AccountType string  `json:"account_type" `
	Password    string  `json:"password" `
	Status      string  `json:"status" `
	Owners      []Owner `json:"owners" `
}

type Owner struct {
	Uid             string `json:"uid" `
	Name            string `json:"name" `
	Gender          string `json:"gender" `
	EmployeeNo      string `json:"employee_no" `
	ShortEmployeeNo string `json:"short_employee_no" `
	Type            string `json:"type" gorm:"type"`
	EmployeeStatus  string `json:"employee_status" `
	Jobs            []Job  `json:"jobs" `
	// Photo           string `json:"photo" `
	// MaritalStatus           string                   `json:"marital_status"`
	// PoliticalStatus         string                   `json:"political_status"`
	// PoliticalStartDate      string                   `json:"political_start_date"`
	// NativeProvince          string                   `json:"native_province"`
	// NativeCity              string                   `json:"native_city"`
	// ResidenceProvince       string                   `json:"residence_province"`
	// ResidenceCity           string                   `json:"residence_city"`
	// IdentityType            string                   `json:"identity_type"`
	// IdentityNo              string                   `json:"identity_no"`
	// Mobile                  string                   `json:"mobile"`
	FixPhone string `json:"fix_phone"`
	Email    string `json:"email"`
	// ConAddress              string                   `json:"con_address"`
	// MajorDesc               string                   `json:"major_desc"`
	// BankNo                  string                   `json:"bank_no"`
	// EducationBackgroundDesc string                   `json:"education_background_desc"`
	ManagerTypeId     string `json:"manager_type_id"`
	ManagerPositionId string `json:"manager_position_id"`
	// ManagerEmpId            string                   `json:"manager_emp_id"`
	// SortNumber              string                   `json:"sort_number"`
	HireDate string `json:"hire_date"`
	// FirstEntryDate          string                   `json:"first_entry_date"`
	// ServiceStartDate        string                   `json:"service_start_date"`
	// TrialPeriodEndDate      string                   `json:"trial_period_end_date"`
	// ConfirmationDate        string                   `json:"confirmation_date"`
	// LastEntryDate           string                   `json:"last_entry_date"`
	// ResignationType         string                   `json:"resignation_type"`
	// UnitDate                string                   `json:"unit_date"`
	// LeaveDate               string                   `json:"leave_date"`
	// Years                   string                   `json:"years"`
	// Months                  string                   `json:"months"`
	// Days                    string                   `json:"days"`
	// SameLevelTenure         string                   `json:"same_level_tenure"`
	// HandoverWorkNo          string                   `json:"handover_work_no"`
	// DepositBank             string                   `json:"deposit_bank"`
	// ExternalWorkExperience []ExternalWorkExperience `json:"external_work_experience"`
	// Extension              Extension                `json:"extension"`
}

type Job struct {
	Org Org `json:"org"`
	// Position Position `json:"position"`
}

type Org struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

type Position struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	OperateType string `json:"operate_type"`
	EmpClass    string `json:"emp_class"`
	PostDutyId  string `json:"post_duty_id"`
	LevelId     string `json:"level_id"`
	CapLevel    string `json:"cap_level"`
}

type ExternalWorkExperience struct {
	ExternalStartTime   string `json:"external_start_time"`
	ExternalEndTime     string `json:"external_end_time"`
	ExternalCompanyName string `json:"external_company_name"`
	ExternalPosition    string `json:"external_position"`
}

type Extension struct {
}

type Orginization struct {
	GroupCode                  string   `json:"group_code"`
	StructCode                 string   `json:"struct_code"`
	Code                       string   `json:"code"`
	ParentCode                 string   `json:"parent_code"`
	Name                       string   `json:"name"`
	ShortName                  string   `json:"short_name"`
	Status                     string   `json:"status"`
	Level                      string   `json:"level"`
	Category                   string   `json:"category"`
	Type                       string   `json:"type"`
	ManagerType                string   `json:"manager_type"`
	ManagerId                  string   `json:"manager_id"`
	ManagerPositionId          string   `json:"manager_position_id"`
	Address                    string   `json:"address"`
	OrgPath                    string   `json:"org_path"`
	SortNumber                 string   `json:"sort_number"`
	FinancialInstitutionCode   string   `json:"financial_institution_code"`
	FinancialInstitutionNumber string   `json:"financial_institution_number"`
	InternalOrganizationNumber string   `json:"internal_organization_number"`
	BranchLeaders              []string `json:"branch_leaders"`
}
