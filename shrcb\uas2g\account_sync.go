package uas2g

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 账户信息
type AccountModel struct {
	gorm.Model
	AccountNo   string `json:"account_no" gorm:"account_no; unique; not null"` // 非空唯一
	AccountName string `json:"account_name" gorm:"account_name"`
	AccountType string `json:"account_type" gorm:"account_type"`
	Password    string `json:"password" gorm:"password"`
	Status      string `json:"status" gorm:"status"`
	OwnerIds    string `json:"owner_ids" gorm:"owner_ids"` // 逗号分隔
}

// user 列表
type OwnerModel struct {
	gorm.Model
	Uid               string `json:"uid" gorm:"uid; unique; not null"` // 非空唯一
	Name              string `json:"name" gorm:"name"`
	Gender            string `json:"gender" gorm:"gender"`
	EmployeeNo        string `json:"employee_no" gorm:"employee_no"`
	ShortEmployeeNo   string `json:"short_employee_no" gorm:"short_employee_no"`
	Type              string `json:"type" gorm:"account_type"`
	EmployeeStatus    string `json:"employee_status" gorm:"employee_status"`
	FixPhone          string `json:"fix_phone" gorm:"fix_phone"`
	Email             string `json:"email" gorm:"email"`
	ManagerTypeId     string `json:"manager_type_id" gorm:"manager_type_id"`
	ManagerPositionId string `json:"manager_position_id" gorm:"manager_position_id"`
	HireDate          string `json:"hire_date" gorm:"hire_date"`
	OrgIds            string `json:"org_ids" gorm:"org_ids"` // 逗号分隔
}

type OrgModel struct {
	gorm.Model
	Code string `json:"code" gorm:"code; unique; not null"` // 非空唯一
	Name string `json:"name" gorm:"name; not null"`
}

// TableName 自定义表名
func (AccountModel) TableName() string {
	return "uas_accounts"
}

// TableName 自定义表名
func (OwnerModel) TableName() string {
	return "uas_account_users"
}

// TableName 自定义表名
func (OrgModel) TableName() string {
	return "uas_account_organizations"
}

func (u *USA2G) accountSync(ctx *gin.Context) {
	var account Account

	// 绑定 JSON 请求体到 account 变量
	err := ctx.BindJSON(&account)
	if err != nil {
		ctx.JSON(400, gin.H{
			"code":    400,
			"message": "解析请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 连接 SQLite 数据库并自动迁移（创建表）
	db := GetSQLite()

	err = db.AutoMigrate(&AccountModel{}, &OwnerModel{}, &OrgModel{})
	if err != nil {
		ctx.JSON(500, gin.H{
			"code":    500,
			"message": "数据库迁移失败",
			"error":   err.Error(),
		})
		return
	}

	// Upsert 账户
	accountModel := &AccountModel{}
	accountModel.AccountNo = account.AccountNo
	accountModel.AccountName = account.AccountName
	accountModel.AccountType = account.AccountType
	accountModel.Password = account.Password
	accountModel.Status = account.Status

	// 收集所有 owner 的 Uid
	var ownerIds []string
	for _, owner := range account.Owners {
		ownerIds = append(ownerIds, owner.Uid)

		ownerModel := &OwnerModel{}
		ownerModel.Uid = owner.Uid
		ownerModel.Name = owner.Name
		ownerModel.Gender = owner.Gender
		ownerModel.EmployeeNo = owner.EmployeeNo
		ownerModel.ShortEmployeeNo = owner.ShortEmployeeNo
		ownerModel.Type = owner.Type
		ownerModel.EmployeeStatus = owner.EmployeeStatus
		ownerModel.FixPhone = owner.FixPhone
		ownerModel.Email = owner.Email
		ownerModel.ManagerTypeId = owner.ManagerTypeId
		ownerModel.ManagerPositionId = owner.ManagerPositionId
		ownerModel.HireDate = owner.HireDate

		var orgIds []string
		for _, job := range owner.Jobs {
			// Upsert 用户的机构列表，逗号分隔，用于查询用户的所有机构时使用
			// ownerModel.OrgIds += job.Org.Code + ","
			orgIds = append(orgIds, job.Org.Code)

			orgModel := &OrgModel{}
			orgModel.Code = job.Org.Code
			orgModel.Name = job.Org.Name
			// Upsert 机构
			var existingOrg OrgModel
			err := db.Where(OrgModel{Code: job.Org.Code}).First(&existingOrg).Error
			if errors.Is(err, gorm.ErrRecordNotFound) {
				db.Create(&orgModel)
			} else if err == nil {
				// 使用 Where 条件进行更新
				db.Where("code = ?", job.Org.Code).Updates(&orgModel)
			}

		}

		if len(orgIds) > 0 {
			ownerModel.OrgIds = strings.Join(orgIds, ",")
		}

		// Upsert 用户
		var existingOwner OwnerModel
		err = db.Where(OwnerModel{Uid: owner.Uid}).First(&existingOwner).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			db.Create(&ownerModel)
		} else {
			// 使用 Where 条件进行更新
			db.Where("uid = ?", owner.Uid).Updates(&ownerModel)
		}

	}

	// 设置 OwnerIds，多个用逗号分隔，单个不加逗号
	if len(ownerIds) > 0 {
		accountModel.OwnerIds = strings.Join(ownerIds, ",")
	}

	// Upsert 账户
	var existingAccount AccountModel
	err2 := db.Where(AccountModel{AccountNo: account.AccountNo}).First(&existingAccount).Error

	// 账户不存在，创建账户；账户存在，更新账户
	if errors.Is(err2, gorm.ErrRecordNotFound) {
		// 创建账户
		if err := db.Create(&accountModel).Error; err != nil {
			ctx.JSON(500, gin.H{
				"code":    500,
				"message": "创建账户失败",
				"error":   err.Error(),
			})
			return
		}
	} else if err2 == nil {
		// 更新账户
		if err := db.Where("account_no = ?", account.AccountNo).Updates(&accountModel).Error; err != nil {
			ctx.JSON(500, gin.H{
				"code":    500,
				"message": "更新账户失败",
				"error":   err.Error(),
			})
			return
		}
	} else {
		ctx.JSON(500, gin.H{
			"code":    500,
			"message": "查询新增失败",
			"error":   err2.Error(),
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code":    0,
		"message": "账户同步成功",
		"data":    "",
	})

}

func (u *USA2G) accountFetchAll(ctx *gin.Context) {
	db := GetSQLite()

	type Data struct {
		Page     int       `json:"page"`     // 当前页码
		Size     int       `json:"size"`     // 每页大小
		Total    int       `json:"total"`    // 总记录数
		Accounts []Account `json:"accounts"` // 结果
	}

	var data Data

	ctx.ShouldBind(&data)

	// 返回结果
	var result []Account

	// 查询所有账户 - 添加分页功能
	var accounts []AccountModel
	var total int64

	// 先统计总记录数
	db.Model(&AccountModel{}).Count(&total)

	// 设置默认分页参数
	if data.Page <= 0 {
		data.Page = 1
	}
	if data.Size <= 0 {
		data.Size = 10
	}

	// 计算偏移量
	offset := (data.Page - 1) * data.Size

	// 分页查询
	db.Limit(data.Size).Offset(offset).Find(&accounts)

	// 设置返回数据
	data.Total = int(total)

	// 查询所有账户的用户列表
	for _, account := range accounts {
		var res Account
		// 填充账户信息
		res.AccountNo = account.AccountNo
		res.AccountName = account.AccountName
		res.AccountType = account.AccountType
		res.Password = account.Password
		res.Status = account.Status

		var owners []OwnerModel
		db.Where("uid IN (?)", strings.Split(account.OwnerIds, ",")).Find(&owners)

		for _, owner := range owners {

			var orgs []OrgModel
			db.Where("code IN (?)", strings.Split(owner.OrgIds, ",")).Find(&orgs)

			var jobs []Job
			for _, org := range orgs {
				var job Job
				job.Org.Code = org.Code
				job.Org.Name = org.Name
				jobs = append(jobs, job)
			}

			res.Owners = append(res.Owners, Owner{
				Uid:               owner.Uid,
				Name:              owner.Name,
				Gender:            owner.Gender,
				EmployeeNo:        owner.EmployeeNo,
				ShortEmployeeNo:   owner.ShortEmployeeNo,
				Type:              owner.Type,
				EmployeeStatus:    owner.EmployeeStatus,
				FixPhone:          owner.FixPhone,
				Email:             owner.Email,
				ManagerTypeId:     owner.ManagerTypeId,
				ManagerPositionId: owner.ManagerPositionId,
				HireDate:          owner.HireDate,
				Jobs:              jobs,
			})

		}

		result = append(result, res)

	}

	// 设置返回数据
	data.Accounts = result

	// 返回结果
	ctx.JSON(200, gin.H{
		"code":    "0",
		"message": "成功",
		"data":    data,
	})
}
