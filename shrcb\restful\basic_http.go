package restful

import "net/http"

// 定义中间件类型
type Middleware func(http.Handler) http.Handler

// Router 封装了 ServeMux 和中间件链
type Router struct {
	mux         *http.ServeMux
	middlewares []Middleware
}

// NewRouter 创建新的路由器实例
func NewRouter() *Router {
	return &Router{
		mux: http.NewServeMux(),
	}
}

// Use 添加全局中间件
func (r *Router) Use(middleware Middleware) {
	r.middlewares = append(r.middlewares, middleware)
}

// HandleFunc 注册路由处理函数（会自动应用中间件）
func (r *Router) HandleFunc(pattern string, handler http.HandlerFunc) {
	r.mux.HandleFunc(pattern, handler)
}

// ServeHTTP 实现 http.Handler 接口
func (r *Router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	// handler := r.mux

	// 使用 http.Handler 接口类型
	var handler http.Handler = r.mux

	// 反向应用中间件（最先添加的中间件最后执行）; eg 洋葱
	for i := len(r.middlewares) - 1; i >= 0; i-- {
		handler = r.middlewares[i](handler)
	}

	// 调用 ServeHTTP 方法
	handler.ServeHTTP(w, req)
}

func TestUse() {
	router := NewRouter()

	// 添加全局中间件
	router.Use(LoggingMiddleware)

	// 注册路由
	router.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// 请求方法在这里获取
		w.Write([]byte("Hello, World!"))
	})

	http.ListenAndServe(":8080", router)
}
