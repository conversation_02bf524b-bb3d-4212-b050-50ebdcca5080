package process

import (
	"bytes"
	"fmt"
	"io"
	"os/user"
	"strconv"
	"strings"
	"time"
	"victoriaMetricsCollector/global"

	"github.com/elastic/go-sysinfo"
	"github.com/elastic/go-sysinfo/types"
	"golang.org/x/text/encoding/unicode"
)

func SysInfo() ([]map[string]string, error) {
	// 获取进程列表
	processes, err := sysinfo.Processes()
	if err != nil {
		return nil, fmt.Errorf("获取进程列表失败: %v", err)
	}

	// 存储结果
	var result []map[string]string

	// 遍历所有进程
	for _, proc := range processes {
		res, err := parseSysInfo(proc)

		if err == nil {
			result = append(result, res)
		}
	}

	global.Setting.Logger.Sugar().Infof("获取到系统中共有 %d 个进程", len(processes))

	return result, nil
}

// parseSysInfo 解析进程详细信息
//
// 该函数从 sysinfo.Process 中获取进程详细信息，并将其
// 转换为 map[string]string 形式返回。
//
// 返回结果:
//   - 一个 map[string]string，其中 key 是列名，value 是对应的字符串值。
func parseSysInfo(process types.Process) (map[string]string, error) {
	// 返回map[string]string
	res := make(map[string]string)

	// 获取进程信息
	info, err := process.Info()
	if err != nil {
		return res, fmt.Errorf("获取进程信息失败: %v", err)
	}

	// 获取进程名称
	name := ensureUTF8(info.Name)

	// 获取进程ID
	pid := fmt.Sprintf("%d", info.PID)

	// 获取父进程ID
	ppid := fmt.Sprintf("%d", info.PPID)

	// 获取进程启动时间
	startTime := info.StartTime.Format(time.RFC3339)

	// 获取进程可执行文件路径
	exe := ensureUTF8(info.Exe)

	// 获取进程命令行参数
	args := info.Args
	var cmdline string
	if name == "java" {
		var line []string
		for _, s := range args {
			if strings.Contains(s, "-") || strings.Contains(s, "_") || strings.Contains(s, "/") {
				continue
			}
			if strings.Contains(s, ".") {
				line = append(line, s)
			}

			if len(line) > 0 {
				cmdline = strings.Join(line, "_")
			}
		}
	} else {
		// 逐步join，限制总长度为200
		var cmdlineParts []string
		currentLength := 0
		for _, arg := range args {
			// 计算加入当前参数后的长度（包括分隔符"_"）
			addLength := len(arg)
			if len(cmdlineParts) > 0 {
				addLength += 1 // 分隔符"_"的长度
			}

			if currentLength+addLength > 1000 {
				break // 超过1000字符，停止join
			}

			cmdlineParts = append(cmdlineParts, arg)
			currentLength += addLength
		}
		cmdline = strings.Join(cmdlineParts, "_")
	}

	// 获取用户信息
	var uid, username string
	userInfo, err := process.User()
	if err == nil {
		// 将UID转换为用户名
		uid = userInfo.UID
		if u, err := user.LookupId(uid); err == nil {
			username = ensureUTF8(u.Username)
		}
	}

	// 获取进程内存使用情况
	var rss, vms string
	if memory, err := process.Memory(); err == nil {
		rss = strconv.FormatUint(memory.Resident, 10)
		vms = strconv.FormatUint(memory.Virtual, 10)
	} else {
		rss = "0"
		vms = "0"
	}

	// 获取进程CPU使用情况
	var cpuTotal, cpuUser, cpuSys, cpuPercent string
	if cpu, err := process.CPUTime(); err == nil {
		cpuTotal = fmt.Sprintf("%f", cpu.Total().Seconds())
		cpuUser = fmt.Sprintf("%f", cpu.User.Seconds())
		cpuSys = fmt.Sprintf("%f", cpu.System.Seconds())

		// 计算cpu 使用率 基于启动时间
		runningTime := time.Since(info.StartTime).Seconds()
		if runningTime > 0 {
			cpuPercent = fmt.Sprintf("%f", (cpu.Total().Seconds()/runningTime)*100)
		}
	} else {
		cpuTotal = "0"
		cpuUser = "0"
		cpuSys = "0"
		cpuTotal = "0"
	}

	// 组装结果
	res["name"] = name
	res["pid"] = pid
	res["ppid"] = ppid
	res["cmdline"] = cmdline
	res["start_time"] = startTime
	res["exe"] = exe
	res["uid"] = uid
	res["username"] = username
	res["rss"] = rss
	res["vms"] = vms
	res["cpu_user"] = cpuUser
	res["cpu_system"] = cpuSys
	res["cpu_total"] = cpuTotal
	res["cpu_percent"] = cpuPercent

	return res, nil
}

func ensureUTF8(s string) string {
	// 如果数据可能是其他编码（如 Windows-1252），可以转换
	utf8Bytes := []byte(s)
	utf8Reader := unicode.UTF8.NewDecoder().Reader(bytes.NewReader(utf8Bytes))
	decoded, _ := io.ReadAll(utf8Reader)
	return string(decoded)
}
