package restful

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
	"victoriaMetricsCollector/global"
)

// loggingMiddleware 是一个记录HTTP访问日志的中间件
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// 创建一个自定义的ResponseWriter来捕获状态码
		lrw := &loggingResponseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		// 调用下一个处理器
		next.ServeHTTP(lrw, r)

		// 计算耗时
		duration := time.Since(start)

		// 判断状态码
		method := r.Method
		status := strconv.Itoa(lrw.statusCode)
		path := r.URL.Path
		ip := getClientIP(r)
		durationStr := duration.String()

		msg := map[string]string{
			"method":   method,
			"status":   status,
			"path":     path,
			"ip":       ip,
			"duration": durationStr,
		}

		jsonMsg, _ := json.Marshal(msg)

		if lrw.statusCode >= 500 {
			global.Setting.Logger.Sugar().Warnf("%v", string(jsonMsg))
		} else if lrw.statusCode >= 400 {
			global.Setting.Logger.Sugar().Warnf("%v", string(jsonMsg))
		} else {
			global.Setting.Logger.Sugar().Infof("%v", string(jsonMsg))
		}

		// // 格式化输出日志: [时间戳] [日志级别] [请求方法] [状态码] [耗时] [请求路径] [客户端IP]
		// fmt.Printf("%s %s %s %s %s %s %s\n",
		// 	formatLogField(timestamp, 24),
		// 	formatLogField(level, 5),
		// 	formatLogField(method, 5),
		// 	formatLogField(status, 5),
		// 	formatLogField(durationStr, 10),
		// 	formatLogField(path, 20),
		// 	formatLogField(ip, 25),
		// )
	})
}

// loggingResponseWriter 用于捕获HTTP状态码
type loggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
}

// WriteHeader 重写以捕获状态码
func (lrw *loggingResponseWriter) WriteHeader(code int) {
	lrw.statusCode = code
	lrw.ResponseWriter.WriteHeader(code)
}

// getClientIP 获取客户端IP地址
func getClientIP(r *http.Request) string {
	ip := r.Header.Get("X-Forwarded-For")
	if ip == "" {
		ip = r.Header.Get("X-Real-IP")
	}
	if ip == "" {
		ip = r.RemoteAddr
	}
	return ip
}

// formatLogField 格式化日志字段到固定宽度
func formatLogField(value string, width int) string {
	if len(value) > width {
		return value[:width-3] + "..."
	}
	return fmt.Sprintf("%-*s", width, value)
}
