package linux

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/ssh"
)

func Basic() {
	// SSH 连接配置
	config := &ssh.ClientConfig{
		User: "username", // 替换为你的用户名
		Auth: []ssh.AuthMethod{
			ssh.Password("password"), // 替换为你的密码
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 忽略主机密钥验证（生产环境不推荐）
		// 生产环境应该使用 ssh.FixedHostKey 或自定义回调验证主机密钥
	}

	// 建立 SSH 连接
	client, err := ssh.Dial("tcp", "hostname:22", config) // 替换为你的主机名和端口
	if err != nil {
		log.Fatalf("Failed to dial: %v", err)
	}
	defer client.Close()

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		log.Fatalf("Failed to create session: %v", err)
	}
	defer session.Close()

	// 设置会话的标准输入、输出和错误
	session.Stdout = os.Stdout
	session.Stderr = os.Stderr
	session.Stdin = os.Stdin

	// 请求伪终端
	modes := ssh.TerminalModes{
		ssh.ECHO:          1,     // 启用回显
		ssh.TTY_OP_ISPEED: 14400, // 输入速度 = 14.4kbaud
		ssh.TTY_OP_OSPEED: 14400, // 输出速度 = 14.4kbaud
	}

	if err := session.RequestPty("xterm", 80, 40, modes); err != nil {
		log.Fatalf("Request for pseudo terminal failed: %v", err)
	}

	// 启动远程 shell
	if err := session.Shell(); err != nil {
		log.Fatalf("Failed to start shell: %v", err)
	}

	// 等待会话结束
	if err := session.Wait(); err != nil {
		log.Fatalf("Session failed: %v", err)
	}
}

func SingleCmd() {
	// SSH 连接配置
	config := &ssh.ClientConfig{
		User: "username",
		Auth: []ssh.AuthMethod{
			ssh.Password("password"),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// 建立 SSH 连接
	client, err := ssh.Dial("tcp", "hostname:22", config)
	if err != nil {
		log.Fatalf("Failed to dial: %v", err)
	}
	defer client.Close()

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		log.Fatalf("Failed to create session: %v", err)
	}
	defer session.Close()

	// 执行命令并获取输出
	cmd := "ls -l /" // 要执行的命令
	output, err := session.CombinedOutput(cmd)
	if err != nil {
		log.Fatalf("Failed to run command: %v", err)
	}

	fmt.Printf("Command output:\n%s\n", output)
}

func WithKey() {
	// 读取私钥文件
	key, err := os.ReadFile("/path/to/private/key") // 替换为你的私钥路径
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}

	// 解析私钥
	signer, err := ssh.ParsePrivateKey(key)
	if err != nil {
		log.Fatalf("Unable to parse private key: %v", err)
	}

	// SSH 连接配置
	config := &ssh.ClientConfig{
		User: "username",
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// 建立 SSH 连接
	client, err := ssh.Dial("tcp", "hostname:22", config)
	if err != nil {
		log.Fatalf("Failed to dial: %v", err)
	}
	defer client.Close()

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		log.Fatalf("Failed to create session: %v", err)
	}
	defer session.Close()

	// 执行命令
	output, err := session.CombinedOutput("uname -a")
	if err != nil {
		log.Fatalf("Failed to run command: %v", err)
	}

	fmt.Printf("System info:\n%s\n", output)
}
