package uas2g

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type OrginizationModel struct {
	GroupCode                  string `json:"group_code" gorm:"group_code"`
	StructCode                 string `json:"struct_code" gorm:"struct_code"`
	Code                       string `json:"code" gorm:"code; unique; not null"` // 非空唯一
	ParentCode                 string `json:"parent_code" gorm:"parent_code"`
	Name                       string `json:"name" gorm:"name"`
	ShortName                  string `json:"short_name" gorm:"short_name"`
	Status                     string `json:"status" gorm:"status"`
	Level                      string `json:"level" gorm:"level"`
	Category                   string `json:"category" gorm:"category"`
	Type                       string `json:"type" gorm:"type"`
	ManagerType                string `json:"manager_type" gorm:"manager_type"`
	ManagerId                  string `json:"manager_id" gorm:"manager_id"`
	ManagerPositionId          string `json:"manager_position_id" gorm:"manager_position_id"`
	Address                    string `json:"address" gorm:"address"`
	OrgPath                    string `json:"org_path" gorm:"org_path"`
	SortNumber                 string `json:"sort_number" gorm:"sort_number"`
	FinancialInstitutionCode   string `json:"financial_institution_code" gorm:"financial_institution_code"`
	FinancialInstitutionNumber string `json:"financial_institution_number" gorm:"financial_institution_number"`
	InternalOrganizationNumber string `json:"internal_organization_number" gorm:"internal_organization_number"`
	BranchLeaders              string `json:"branch_leaders" gorm:"branch_leaders"` // 逗号分隔
}

// TableName 自定义表名
func (OrginizationModel) TableName() string {
	return "uas_organizations"
}

func (u *USA2G) orgSync(ctx *gin.Context) {
	var orginization Orginization

	// 绑定 JSON 请求体到 account 变量
	err := ctx.BindJSON(&orginization)
	if err != nil {
		ctx.JSON(400, gin.H{"解析请求参数错误: ": err.Error()})
		return
	}

	// 连接 SQLite 数据库并自动迁移（创建表）
	db := GetSQLite()

	err = db.AutoMigrate(&OrginizationModel{})
	if err != nil {
		ctx.JSON(500, gin.H{"数据库迁移失败: ": err.Error()})
		return
	}

	// Upsert 机构
	orginizationModel := &OrginizationModel{}
	orginizationModel.GroupCode = orginization.GroupCode
	orginizationModel.StructCode = orginization.StructCode
	orginizationModel.Code = orginization.Code
	orginizationModel.ParentCode = orginization.ParentCode
	orginizationModel.Name = orginization.Name
	orginizationModel.ShortName = orginization.ShortName
	orginizationModel.Status = orginization.Status
	orginizationModel.Level = orginization.Level
	orginizationModel.Category = orginization.Category
	orginizationModel.Type = orginization.Type
	orginizationModel.ManagerType = orginization.ManagerType
	orginizationModel.ManagerId = orginization.ManagerId
	orginizationModel.ManagerPositionId = orginization.ManagerPositionId
	orginizationModel.Address = orginization.Address
	orginizationModel.OrgPath = orginization.OrgPath
	orginizationModel.SortNumber = orginization.SortNumber
	orginizationModel.FinancialInstitutionCode = orginization.FinancialInstitutionCode
	orginizationModel.FinancialInstitutionNumber = orginization.FinancialInstitutionNumber
	orginizationModel.InternalOrganizationNumber = orginization.InternalOrganizationNumber
	orginizationModel.BranchLeaders = strings.Join(orginization.BranchLeaders, ",")

	var existingOrginization OrginizationModel
	err = db.Where(OrginizationModel{Code: orginization.Code}).First(&existingOrginization).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		db.Create(&orginizationModel)
	} else if err == nil {
		// 使用 Where 条件进行更新
		db.Where("code = ?", orginization.Code).Updates(&orginizationModel)
	}

	ctx.JSON(200, gin.H{
		"code":    0,
		"message": "机构同步成功",
		"data":    "",
	})

}
