package db

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// 定义一个User结构体，用于映射数据库表
type User struct {
	ID        int
	Name      string
	Email     string
	CreatedAt time.Time
}

func Test() {
	// 数据库连接配置
	dbUser := "your_username"
	dbPass := "your_password"
	dbHost := "localhost"
	dbPort := "3306"
	dbName := "testdb"

	// 构建DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true", dbUser, dbPass, dbHost, dbPort, dbName)

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 测试连接
	err = db.<PERSON>()
	if err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}
	fmt.Println("Successfully connected to MySQL database!")

	// 创建表
	createTable(db)

	// 插入数据
	userID := insertUser(db, "Alice", "<EMAIL>")
	fmt.Printf("Inserted user with ID: %d\n", userID)

	// 查询单个用户
	user := getUser(db, userID)
	fmt.Printf("Retrieved user: %+v\n", user)

	// 查询多个用户
	users := getUsers(db)
	fmt.Println("All users:")
	for _, u := range users {
		fmt.Printf("  %+v\n", u)
	}

	// 更新用户
	rowsAffected := updateUser(db, userID, "Alice Updated")
	fmt.Printf("Updated %d user(s)\n", rowsAffected)

	// 删除用户
	rowsAffected = deleteUser(db, userID)
	fmt.Printf("Deleted %d user(s)\n", rowsAffected)
}

func createTable(db *sql.DB) {
	query := `
		CREATE TABLE IF NOT EXISTS users (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(50) NOT NULL,
			email VARCHAR(50) NOT NULL UNIQUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`

	_, err := db.Exec(query)
	if err != nil {
		log.Fatalf("Failed to create table: %v", err)
	}
	fmt.Println("Table created successfully (or already exists)")
}

func insertUser(db *sql.DB, name, email string) int64 {
	query := `INSERT INTO users (name, email) VALUES (?, ?)`
	result, err := db.Exec(query, name, email)
	if err != nil {
		log.Fatalf("Failed to insert user: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		log.Fatalf("Failed to get last insert ID: %v", err)
	}

	return id
}

func getUser(db *sql.DB, id int64) *User {
	query := `SELECT id, name, email, created_at FROM users WHERE id = ?`
	row := db.QueryRow(query, id)

	var user User
	err := row.Scan(&user.ID, &user.Name, &user.Email, &user.CreatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			fmt.Println("No user found with ID:", id)
			return nil
		}
		log.Fatalf("Failed to scan user: %v", err)
	}

	return &user
}

func getUsers(db *sql.DB) []User {
	query := `SELECT id, name, email, created_at FROM users`
	rows, err := db.Query(query)
	if err != nil {
		log.Fatalf("Failed to query users: %v", err)
	}
	defer rows.Close()

	var users []User
	for rows.Next() {
		var user User
		err := rows.Scan(&user.ID, &user.Name, &user.Email, &user.CreatedAt)
		if err != nil {
			log.Fatalf("Failed to scan user: %v", err)
		}
		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		log.Fatalf("Error during rows iteration: %v", err)
	}

	return users
}

func updateUser(db *sql.DB, id int64, newName string) int64 {
	query := `UPDATE users SET name = ? WHERE id = ?`
	result, err := db.Exec(query, newName, id)
	if err != nil {
		log.Fatalf("Failed to update user: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Fatalf("Failed to get rows affected: %v", err)
	}

	return rowsAffected
}

func deleteUser(db *sql.DB, id int64) int64 {
	query := `DELETE FROM users WHERE id = ?`
	result, err := db.Exec(query, id)
	if err != nil {
		log.Fatalf("Failed to delete user: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.Fatalf("Failed to get rows affected: %v", err)
	}

	return rowsAffected
}
