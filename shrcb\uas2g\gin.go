package uas2g

import (
	"strconv"
	"time"
	"victoriaMetricsCollector/global"

	"github.com/gin-gonic/gin"
)

func NewUSA2G(port int) *USA2G {

	gin.SetMode(gin.ReleaseMode)

	us := &USA2G{
		engine: gin.Default(),
	}

	// 配置路由
	us.router()

	// 启动服务
	us.run(port)

	return us
}

type USA2G struct {
	engine *gin.Engine
}

func (u *USA2G) router() {
	// 登录接口（不需要认证）
	u.engine.POST("/api/v1/login", u.login)

	// 需要认证的接口组
	auth := u.engine.Group("/api/v1")
	auth.Use(JWTAuthMiddleware())
	{
		auth.POST("/org/sync", u.orgSync)
		auth.POST("/user/sync", u.accountSync)
		auth.POST("/user/list", u.accountFetchAll)
	}
}

// 启动HTTP服务
func (u *USA2G) run(port int) {
	err := u.engine.Run(":" + strconv.Itoa(port))
	if err != nil {
		panic(err)
	}
}

// 登录接口，返回token
func (u *USA2G) login(c *gin.Context) {
	// 这里可以添加用户验证逻辑

	// 获取过期时间
	duration := time.Duration(global.Setting.StaticConfig.JWTConfig.JwtExpireDuration) * time.Second

	// 生成token（有效期N秒）
	token, err := GenerateToken(duration)
	if err != nil {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "生成token失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"code":    200,
		"message": "登录成功",
		"token":   token,
	})
}
